# 文档解析器配置和对比说明

parsers:
  # MarkItDown - 微软开源统一转换工具
  markitdown:
    description: "微软开源的统一文档转换工具"
    supported_formats: ["docx", "pptx", "xlsx", "pdf"]
    advantages:
      - "统一接口，一个库处理多种格式"
      - "微软官方支持，质量有保障"
      - "直接输出Markdown格式"
      - "支持图片和表格转换"
    disadvantages:
      - "相对较新，生态不够成熟"
      - "自定义能力有限"
      - "对复杂格式支持可能不够精细"
    use_cases: ["快速原型", "统一处理流程", "简单文档转换"]
    
  # python-docx - Word文档专业处理
  python_docx:
    description: "专门处理Word文档的成熟库"
    supported_formats: ["docx"]
    advantages:
      - "功能强大，支持复杂Word文档结构"
      - "可以提取样式、表格、图片等详细信息"
      - "社区成熟，文档完善"
      - "精确控制文档解析过程"
    disadvantages:
      - "只支持docx格式，不支持doc"
      - "需要自己实现Markdown转换逻辑"
      - "学习成本相对较高"
    use_cases: ["复杂Word文档处理", "需要保留格式信息", "精细化控制"]
    
  # python-pptx - PowerPoint专业处理
  python_pptx:
    description: "专门处理PowerPoint的库"
    supported_formats: ["pptx"]
    advantages:
      - "可以提取幻灯片文本、图片、形状"
      - "支持幻灯片结构分析"
      - "可以获取演讲者备注"
    disadvantages:
      - "只支持pptx，不支持ppt"
      - "需要自定义Markdown转换"
      - "对复杂动画和效果支持有限"
    use_cases: ["演示文稿内容提取", "幻灯片结构分析"]
    
  # openpyxl - Excel专业处理
  openpyxl:
    description: "处理Excel文件的标准库"
    supported_formats: ["xlsx"]
    advantages:
      - "功能全面，支持公式、图表、样式"
      - "可以精确读取单元格数据"
      - "支持工作表结构分析"
    disadvantages:
      - "只支持xlsx，需要xlrd处理xls"
      - "表格转Markdown需要自定义逻辑"
    use_cases: ["表格数据提取", "数据分析", "结构化信息处理"]

# 推荐策略
strategy:
  hybrid_approach:
    description: "混合使用多种解析器"
    workflow:
      - "首先尝试MarkItDown进行快速转换"
      - "如果需要精细控制，使用专业库"
      - "根据文档复杂度动态选择解析器"
    
  fallback_chain:
    description: "解析器降级策略"
    chain:
      - "primary: MarkItDown"
      - "fallback: 专业库 (python-docx/python-pptx/openpyxl)"
      - "final: 基础文本提取"
