"""
演示简化后的路径处理方式
"""

from pathlib import Path
from typing import Union
from src.parsers import parse_document


def demo_path_handling():
    """演示不同路径输入方式"""
    
    print("=== 路径处理演示 ===\n")
    
    # 不同的路径输入方式
    test_paths = [
        "data/documents/docx/test.docx",  # 字符串路径
        Path("data/documents/docx/test.docx"),  # Path对象
        "data\\documents\\docx\\test.docx",  # Windows风格路径
        Path("data") / "documents" / "docx" / "test.docx",  # Path拼接
    ]
    
    print("1. 路径标准化演示:")
    for i, test_path in enumerate(test_paths, 1):
        normalized = Path(test_path)
        print(f"   输入{i}: {test_path} ({type(test_path).__name__})")
        print(f"   输出: {normalized} ({type(normalized).__name__})")
        print(f"   相等: {normalized == Path('data/documents/docx/test.docx')}")
        print()
    
    print("2. 解析器工厂路径处理:")
    # 演示解析器如何处理不同路径类型
    from src.parsers import DocumentParserFactory
    factory = DocumentParserFactory()
    
    for path_input in ["data/test.docx", Path("data/test.docx")]:
        is_supported = factory.is_supported(path_input)
        print(f"   {path_input} ({type(path_input).__name__}) -> 支持: {is_supported}")
    
    print("\n3. 配置管理路径处理:")
    from config import get_config
    config = get_config()
    
    # 演示配置中的路径处理
    print(f"   项目根目录: {config.paths.project_root}")
    print(f"   文档目录: {config.paths.documents_dir}")
    
    # 演示相对路径计算
    from config.settings import get_relative_path
    abs_path = config.paths.documents_dir / "docx" / "test.docx"
    rel_path = get_relative_path(abs_path)
    print(f"   绝对路径: {abs_path}")
    print(f"   相对路径: {rel_path}")


def demo_pathlib_features():
    """演示pathlib的强大功能"""
    
    print("\n=== pathlib 功能演示 ===\n")
    
    # 创建示例路径
    path = Path("data/documents/docx/example.docx")
    
    print("1. 路径属性:")
    print(f"   完整路径: {path}")
    print(f"   文件名: {path.name}")
    print(f"   文件名(无扩展名): {path.stem}")
    print(f"   扩展名: {path.suffix}")
    print(f"   父目录: {path.parent}")
    print(f"   路径组件: {path.parts}")
    
    print("\n2. 路径操作:")
    print(f"   绝对路径: {path.resolve()}")
    print(f"   是否存在: {path.exists()}")
    print(f"   是否为文件: {path.is_file()}")
    print(f"   是否为目录: {path.is_dir()}")
    
    print("\n3. 路径拼接:")
    base = Path("data")
    result1 = base / "documents" / "docx" / "test.docx"
    result2 = base.joinpath("documents", "docx", "test.docx")
    print(f"   使用 / 操作符: {result1}")
    print(f"   使用 joinpath: {result2}")
    print(f"   结果相等: {result1 == result2}")
    
    print("\n4. 文件查找:")
    docs_dir = Path("data/documents")
    if docs_dir.exists():
        print(f"   查找所有文件: {list(docs_dir.rglob('*'))[:5]}...")  # 只显示前5个
        print(f"   查找docx文件: {list(docs_dir.rglob('*.docx'))}")


if __name__ == "__main__":
    demo_path_handling()
    demo_pathlib_features()
