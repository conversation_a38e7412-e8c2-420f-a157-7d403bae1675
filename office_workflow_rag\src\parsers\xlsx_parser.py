"""
Excel解析器
使用openpyxl库进行Excel文档处理
"""

from pathlib import Path
from typing import Dict, Any, List
import logging

from .base_parser import BaseParser, ParseResult

try:
    from openpyxl import load_workbook
    from openpyxl.workbook import Workbook
    from openpyxl.worksheet.worksheet import Worksheet
except ImportError:
    load_workbook = None
    Workbook = None
    Worksheet = None

try:
    import xlrd
except ImportError:
    xlrd = None

logger = logging.getLogger(__name__)


class XlsxParser(BaseParser):
    """Excel解析器"""
    
    def __init__(self):
        if load_workbook is None:
            raise ImportError("openpyxl library not installed. Run: uv pip install openpyxl")
    
    def parse(self, file_path: Path, **kwargs) -> ParseResult:
        """
        解析Excel文档
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
                - max_rows: 最大行数 (默认1000)
                - include_formulas: 是否包含公式 (默认False)
                
        Returns:
            ParseResult: 解析结果
        """
        max_rows = kwargs.get('max_rows', 1000)
        include_formulas = kwargs.get('include_formulas', False)
        
        try:
            # 处理老版本xls文件
            if file_path.suffix.lower() == '.xls':
                return self._parse_xls(file_path, max_rows)
            
            # 处理xlsx文件
            wb = load_workbook(str(file_path), data_only=not include_formulas)
            
            content_parts = []
            markdown_parts = []
            sheet_info = []
            
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                sheet_content = self._extract_sheet_content(ws, max_rows, include_formulas)
                
                sheet_info.append({
                    'sheet_name': sheet_name,
                    'row_count': sheet_content['row_count'],
                    'col_count': sheet_content['col_count'],
                    'has_data': sheet_content['has_data']
                })
                
                if sheet_content['has_data']:
                    content_parts.append(f"Sheet: {sheet_name}\n{sheet_content['text']}")
                    markdown_parts.append(f"## Sheet: {sheet_name}\n{sheet_content['markdown']}")
            
            return ParseResult(
                content='\n\n'.join(content_parts),
                markdown='\n\n'.join(markdown_parts),
                metadata={
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'file_type': file_path.suffix.lower(),
                    'parser': 'openpyxl',
                    'sheet_count': len(wb.sheetnames),
                    'sheet_info': sheet_info,
                    'core_properties': self._extract_core_properties(wb)
                },
                success=True,
                parser_used='XlsxParser'
            )
            
        except Exception as e:
            logger.error(f"Excel parsing failed for {file_path}: {e}")
            return ParseResult(
                content='',
                markdown='',
                metadata={
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'file_type': file_path.suffix.lower(),
                    'parser': 'openpyxl'
                },
                success=False,
                error=str(e),
                parser_used='XlsxParser'
            )
    
    def _parse_xls(self, file_path: Path, max_rows: int) -> ParseResult:
        """解析老版本xls文件"""
        if xlrd is None:
            return ParseResult(
                content='',
                markdown='',
                metadata={'file_path': str(file_path)},
                success=False,
                error="xlrd library not installed for .xls files",
                parser_used='XlsxParser'
            )
        
        try:
            wb = xlrd.open_workbook(str(file_path))
            content_parts = []
            markdown_parts = []
            
            for sheet in wb.sheets():
                sheet_content = self._extract_xls_sheet_content(sheet, max_rows)
                if sheet_content['has_data']:
                    content_parts.append(f"Sheet: {sheet.name}\n{sheet_content['text']}")
                    markdown_parts.append(f"## Sheet: {sheet.name}\n{sheet_content['markdown']}")
            
            return ParseResult(
                content='\n\n'.join(content_parts),
                markdown='\n\n'.join(markdown_parts),
                metadata={
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'file_type': '.xls',
                    'parser': 'xlrd'
                },
                success=True,
                parser_used='XlsxParser'
            )
            
        except Exception as e:
            logger.error(f"XLS parsing failed for {file_path}: {e}")
            return ParseResult(
                content='',
                markdown='',
                metadata={'file_path': str(file_path)},
                success=False,
                error=str(e),
                parser_used='XlsxParser'
            )
    
    def _extract_sheet_content(self, ws, max_rows: int, include_formulas: bool) -> Dict[str, Any]:
        """提取工作表内容"""
        rows = []
        markdown_rows = []
        row_count = 0
        col_count = 0
        has_data = False
        
        for row_idx, row in enumerate(ws.iter_rows(max_row=max_rows), 1):
            if row_idx > max_rows:
                break
                
            row_values = []
            for cell in row:
                if cell.value is not None:
                    if include_formulas and cell.data_type == 'f':
                        row_values.append(f"={cell.value}")
                    else:
                        row_values.append(str(cell.value))
                    has_data = True
                else:
                    row_values.append('')
            
            if any(row_values):  # 只添加非空行
                rows.append(' | '.join(row_values))
                
                # Markdown表格格式
                if row_idx == 1:  # 表头
                    markdown_rows.append('| ' + ' | '.join(row_values) + ' |')
                    markdown_rows.append('| ' + ' | '.join(['---'] * len(row_values)) + ' |')
                else:
                    markdown_rows.append('| ' + ' | '.join(row_values) + ' |')
                
                row_count = row_idx
                col_count = max(col_count, len(row_values))
        
        return {
            'text': '\n'.join(rows),
            'markdown': '\n'.join(markdown_rows),
            'row_count': row_count,
            'col_count': col_count,
            'has_data': has_data
        }
    
    def _extract_xls_sheet_content(self, sheet, max_rows: int) -> Dict[str, Any]:
        """提取xls工作表内容"""
        rows = []
        markdown_rows = []
        has_data = False
        
        for row_idx in range(min(sheet.nrows, max_rows)):
            row_values = []
            for col_idx in range(sheet.ncols):
                cell_value = sheet.cell_value(row_idx, col_idx)
                if cell_value:
                    row_values.append(str(cell_value))
                    has_data = True
                else:
                    row_values.append('')
            
            if any(row_values):
                rows.append(' | '.join(row_values))
                
                if row_idx == 0:  # 表头
                    markdown_rows.append('| ' + ' | '.join(row_values) + ' |')
                    markdown_rows.append('| ' + ' | '.join(['---'] * len(row_values)) + ' |')
                else:
                    markdown_rows.append('| ' + ' | '.join(row_values) + ' |')
        
        return {
            'text': '\n'.join(rows),
            'markdown': '\n'.join(markdown_rows),
            'has_data': has_data
        }
    
    def _extract_core_properties(self, wb) -> Dict[str, Any]:
        """提取工作簿核心属性"""
        props = wb.properties
        return {
            'title': props.title,
            'creator': props.creator,
            'subject': props.subject,
            'created': props.created.isoformat() if props.created else None,
            'modified': props.modified.isoformat() if props.modified else None,
        }
    
    def is_supported(self, file_path: Path) -> bool:
        """检查文件是否支持"""
        return file_path.suffix.lower() in ['.xlsx', '.xls']
    
    @staticmethod
    def get_supported_formats():
        """获取支持的文件格式"""
        return ['.xlsx', '.xls']
