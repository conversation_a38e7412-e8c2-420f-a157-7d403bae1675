"""
文档解析器模块

支持多种Office文档格式的解析：
- MarkItDown: 统一转换工具
- python-docx: Word文档专业处理
- python-pptx: PowerPoint专业处理  
- openpyxl: Excel专业处理

解析策略：
- markitdown_only: 仅使用MarkItDown
- specialized_only: 仅使用专业库
- hybrid: 混合模式（推荐）
- auto: 自动选择
"""

from .parser_factory import DocumentParserFactory, ParserStrategy, create_parser_factory
from .markitdown_parser import MarkItDownParser
from .docx_parser import DocxParser
from .pptx_parser import PptxParser
from .xlsx_parser import XlsxParser

__all__ = [
    'DocumentParserFactory',
    'ParserStrategy', 
    'create_parser_factory',
    'MarkItDownParser',
    'DocxParser',
    'PptxParser',
    'XlsxParser'
]
