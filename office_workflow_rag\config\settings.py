"""
项目配置管理
优雅的路径处理和配置管理
"""

import os
from pathlib import Path
from typing import Dict, List, Union
from pydantic import BaseModel, Field, field_validator

# 尝试加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # 如果没有安装python-dotenv，跳过
    pass


class PathConfig(BaseModel):
    """路径配置类"""
    
    # 项目根目录
    project_root: Path = Field(default_factory=lambda: Path(__file__).parent.parent)
    
    # 数据目录
    data_dir: Path = Field(default=None)
    documents_dir: Path = Field(default=None)
    processed_dir: Path = Field(default=None)
    vector_db_dir: Path = Field(default=None)
    
    # 配置目录
    config_dir: Path = Field(default=None)
    
    def __init__(self, **data):
        super().__init__(**data)
        self._setup_paths()
    
    def _setup_paths(self):
        """设置所有路径"""
        if self.data_dir is None:
            self.data_dir = self.project_root / "data"
        
        if self.documents_dir is None:
            self.documents_dir = self.data_dir / "documents"
        
        if self.processed_dir is None:
            self.processed_dir = self.data_dir / "processed"
        
        if self.vector_db_dir is None:
            self.vector_db_dir = self.data_dir / "vector_db"
        
        if self.config_dir is None:
            self.config_dir = self.project_root / "config"
    
    def get_document_type_dirs(self) -> Dict[str, Path]:
        """获取按文档类型分类的目录"""
        doc_types = ["docx", "doc", "pptx", "ppt", "xlsx", "xls"]
        return {
            doc_type: self.documents_dir / doc_type 
            for doc_type in doc_types
        }
    
    def get_processed_dirs(self) -> Dict[str, Path]:
        """获取处理后文件的目录"""
        return {
            "markdown": self.processed_dir / "markdown",
            "chunks": self.processed_dir / "chunks"
        }
    
    def ensure_directories_exist(self):
        """确保所有目录存在"""
        directories = [
            self.data_dir,
            self.documents_dir,
            self.processed_dir,
            self.vector_db_dir,
            self.config_dir
        ]
        
        # 添加文档类型目录
        directories.extend(self.get_document_type_dirs().values())
        
        # 添加处理后文件目录
        directories.extend(self.get_processed_dirs().values())
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @field_validator('project_root', 'data_dir', 'documents_dir', 'processed_dir', 'vector_db_dir', 'config_dir')
    @classmethod
    def validate_paths(cls, v):
        """验证路径"""
        if v is not None and isinstance(v, str):
            return Path(v)
        return v


class LLMConfig(BaseModel):
    """大模型配置"""
    
    openai_api_key: str = Field(default_factory=lambda: os.getenv("OPENAI_API_KEY", ""))
    openai_base_url: str = Field(default_factory=lambda: os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"))
    openai_model: str = Field(default_factory=lambda: os.getenv("OPENAI_MODEL", "gpt-4o-mini"))
    openai_embedding_model: str = Field(default_factory=lambda: os.getenv("OPENAI_EMBEDDING_MODEL", "text-embedding-3-small"))


class VectorDBConfig(BaseModel):
    """向量数据库配置"""
    
    qdrant_url: str = Field(default_factory=lambda: os.getenv("QDRANT_URL", "http://localhost:6333"))
    qdrant_collection_name: str = Field(default_factory=lambda: os.getenv("QDRANT_COLLECTION_NAME", "office_documents"))


class ProcessingConfig(BaseModel):
    """文档处理配置"""
    
    max_chunk_size: int = Field(default_factory=lambda: int(os.getenv("MAX_CHUNK_SIZE", "1000")))
    chunk_overlap: int = Field(default_factory=lambda: int(os.getenv("CHUNK_OVERLAP", "200")))
    batch_size: int = Field(default_factory=lambda: int(os.getenv("BATCH_SIZE", "10")))


class AppConfig(BaseModel):
    """应用总配置"""
    
    paths: PathConfig = Field(default_factory=PathConfig)
    llm: LLMConfig = Field(default_factory=LLMConfig)
    vector_db: VectorDBConfig = Field(default_factory=VectorDBConfig)
    processing: ProcessingConfig = Field(default_factory=ProcessingConfig)
    
    log_level: str = Field(default_factory=lambda: os.getenv("LOG_LEVEL", "INFO"))
    
    def __init__(self, **data):
        super().__init__(**data)
        # 确保目录存在
        self.paths.ensure_directories_exist()


# 全局配置实例
config = AppConfig()


def get_config() -> AppConfig:
    """获取配置实例"""
    return config


def normalize_path(file_path: Union[str, Path]) -> Path:
    """标准化路径处理的全局函数"""
    return Path(file_path)


def get_relative_path(file_path: Union[str, Path], base_path: Union[str, Path] = None) -> Path:
    """获取相对路径"""
    path = Path(file_path)
    base = Path(base_path) if base_path else config.paths.project_root
    
    try:
        return path.relative_to(base)
    except ValueError:
        # 如果无法计算相对路径，返回绝对路径
        return path.resolve()


def find_files_by_extension(directory: Union[str, Path], extensions: List[str]) -> List[Path]:
    """在目录中查找指定扩展名的文件"""
    dir_path = Path(directory)
    
    if not dir_path.exists() or not dir_path.is_dir():
        return []
    
    found_files = []
    for ext in extensions:
        # 确保扩展名以点开头
        if not ext.startswith('.'):
            ext = f'.{ext}'
        
        # 查找文件（不区分大小写）
        pattern = f"*{ext}"
        found_files.extend(dir_path.glob(pattern))
        
        # 也查找大写版本
        if ext.lower() != ext.upper():
            pattern_upper = f"*{ext.upper()}"
            found_files.extend(dir_path.glob(pattern_upper))
    
    return sorted(list(set(found_files)))  # 去重并排序
