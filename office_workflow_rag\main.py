"""
主程序入口
演示解析器工厂的使用
"""

import logging
from pathlib import Path
from src.parsers import DocumentParserFactory, parse_document

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    logger.info("Office Workflow RAG System - Parser Demo")
    
    # 创建解析器工厂
    factory = DocumentParserFactory()
    
    # 显示支持的格式
    supported_formats = factory.get_supported_formats()
    logger.info(f"Supported formats: {supported_formats}")
    
    # 检查文档目录
    doc_dirs = [
        Path("data/documents/docx"),
        Path("data/documents/pptx"),
        Path("data/documents/xlsx"),
        Path("data/documents/doc"),
        Path("data/documents/ppt"),
        Path("data/documents/xls")
    ]
    
    found_files = []
    for doc_dir in doc_dirs:
        if doc_dir.exists():
            for file_path in doc_dir.iterdir():
                if file_path.is_file() and factory.is_supported(file_path):
                    found_files.append(file_path)
    
    if not found_files:
        logger.info("No supported documents found in data/documents/ directories")
        logger.info("Please add some .docx, .pptx, or .xlsx files to test the parser")
        return
    
    # 解析找到的文件
    for file_path in found_files:
        logger.info(f"\n{'='*50}")
        logger.info(f"Parsing: {file_path}")
        
        try:
            result = factory.parse_document(file_path)
            
            if result.success:
                logger.info(f"✅ Success - Parser used: {result.parser_used}")
                logger.info(f"Content length: {len(result.content)} characters")
                logger.info(f"Metadata: {result.metadata}")
                
                # 显示内容预览
                preview = result.content[:200] + "..." if len(result.content) > 200 else result.content
                logger.info(f"Content preview: {preview}")
                
            else:
                logger.error(f"❌ Failed - Error: {result.error}")
                
        except Exception as e:
            logger.error(f"❌ Exception occurred: {e}")


def demo_parse_single_file(file_path: str):
    """演示解析单个文件"""
    path = Path(file_path)
    
    if not path.exists():
        logger.error(f"File not found: {file_path}")
        return
    
    logger.info(f"Parsing single file: {file_path}")
    result = parse_document(path)
    
    if result.success:
        logger.info(f"✅ Success - Parser: {result.parser_used}")
        print(f"\nContent:\n{result.content[:500]}...")
        print(f"\nMarkdown:\n{result.markdown[:500]}...")
    else:
        logger.error(f"❌ Failed: {result.error}")


if __name__ == "__main__":
    main()
    
    # 如果要测试单个文件，取消注释下面的行
    # demo_parse_single_file("path/to/your/document.docx")
