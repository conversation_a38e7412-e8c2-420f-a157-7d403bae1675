"""
主程序入口
演示解析器工厂的使用
"""

import logging
from pathlib import Path
from typing import List
from src.parsers import DocumentParserFactory, parse_document
from config.settings import get_config, find_files_by_extension

# 获取配置
config = get_config()

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def find_supported_files(factory: DocumentParserFactory) -> List[Path]:
    """查找所有支持的文档文件"""
    found_files = []

    # 获取所有文档类型目录
    doc_type_dirs = config.paths.get_document_type_dirs()

    for doc_type, doc_dir in doc_type_dirs.items():
        if not doc_dir.exists():
            logger.debug(f"Directory not found: {doc_dir}")
            continue

        # 查找该类型的文件
        extensions = [f".{doc_type}"]
        type_files = find_files_by_extension(doc_dir, extensions)

        # 过滤支持的文件
        for file_path in type_files:
            if factory.is_supported(file_path):
                found_files.append(file_path)
                logger.debug(f"Found supported file: {file_path}")

    return found_files


def main():
    """主函数"""
    logger.info("Office Workflow RAG System - Parser Demo")
    logger.info(f"Project root: {config.paths.project_root}")
    logger.info(f"Documents directory: {config.paths.documents_dir}")

    # 创建解析器工厂
    factory = DocumentParserFactory()

    # 显示支持的格式
    supported_formats = factory.get_supported_formats()
    logger.info(f"Supported formats: {supported_formats}")

    # 查找支持的文件
    found_files = find_supported_files(factory)

    if not found_files:
        logger.info(f"No supported documents found in {config.paths.documents_dir}")
        logger.info("Please add some .docx, .pptx, or .xlsx files to test the parser")
        logger.info("Available directories:")
        for doc_type, doc_dir in config.paths.get_document_type_dirs().items():
            logger.info(f"  - {doc_type}: {doc_dir}")
        return
    
    # 解析找到的文件
    for file_path in found_files:
        logger.info(f"\n{'='*50}")
        logger.info(f"Parsing: {file_path}")
        
        try:
            result = factory.parse_document(file_path)
            
            if result.success:
                logger.info(f"✅ Success - Parser used: {result.parser_used}")
                logger.info(f"Content length: {len(result.content)} characters")
                logger.info(f"Metadata: {result.metadata}")
                
                # 显示内容预览
                preview = result.content[:200] + "..." if len(result.content) > 200 else result.content
                logger.info(f"Content preview: {preview}")
                
            else:
                logger.error(f"❌ Failed - Error: {result.error}")
                
        except Exception as e:
            logger.error(f"❌ Exception occurred: {e}")


def demo_parse_single_file(file_path: str | Path):
    """演示解析单个文件"""
    # 统一转换为Path对象
    path = Path(file_path) if isinstance(file_path, str) else file_path

    if not path.exists():
        logger.error(f"File not found: {path}")
        return

    if not path.is_file():
        logger.error(f"Not a file: {path}")
        return

    logger.info(f"Parsing single file: {path}")
    result = parse_document(path)

    if result.success:
        logger.info(f"✅ Success - Parser: {result.parser_used}")
        print(f"\nContent:\n{result.content[:500]}...")
        print(f"\nMarkdown:\n{result.markdown[:500]}...")
    else:
        logger.error(f"❌ Failed: {result.error}")


if __name__ == "__main__":
    main()
    
    # 如果要测试单个文件，取消注释下面的行
    # demo_parse_single_file("path/to/your/document.docx")
