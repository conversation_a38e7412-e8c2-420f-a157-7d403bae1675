"""
PowerPoint解析器
使用python-pptx库进行PowerPoint文档处理
"""

from pathlib import Path
from typing import Dict, Any, List
import logging

from .base_parser import BaseParser, ParseResult

try:
    from pptx import Presentation
    from pptx.slide import Slide
except ImportError:
    Presentation = None

logger = logging.getLogger(__name__)


class PptxParser(BaseParser):
    """PowerPoint解析器"""
    
    def __init__(self):
        if Presentation is None:
            raise ImportError("python-pptx library not installed. Run: uv pip install python-pptx")
    
    def parse(self, file_path: Path, **kwargs) -> ParseResult:
        """
        解析PowerPoint文档
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
                - include_notes: 是否包含演讲者备注 (默认True)
                - slide_separator: 幻灯片分隔符 (默认'---')
                
        Returns:
            ParseResult: 解析结果
        """
        include_notes = kwargs.get('include_notes', True)
        slide_separator = kwargs.get('slide_separator', '---')
        
        try:
            prs = Presentation(str(file_path))
            
            content_parts = []
            markdown_parts = []
            slide_info = []
            
            for i, slide in enumerate(prs.slides, 1):
                slide_content = self._extract_slide_content(slide, include_notes)
                slide_info.append({
                    'slide_number': i,
                    'title': slide_content.get('title', ''),
                    'text_count': len(slide_content.get('texts', [])),
                    'has_notes': bool(slide_content.get('notes', ''))
                })
                
                # 组装幻灯片内容
                slide_text_parts = []
                slide_md_parts = []
                
                # 标题
                if slide_content.get('title'):
                    slide_text_parts.append(f"Slide {i}: {slide_content['title']}")
                    slide_md_parts.append(f"## Slide {i}: {slide_content['title']}")
                else:
                    slide_text_parts.append(f"Slide {i}")
                    slide_md_parts.append(f"## Slide {i}")
                
                # 文本内容
                for text in slide_content.get('texts', []):
                    slide_text_parts.append(text)
                    slide_md_parts.append(text)
                
                # 演讲者备注
                if include_notes and slide_content.get('notes'):
                    slide_text_parts.append(f"Notes: {slide_content['notes']}")
                    slide_md_parts.append(f"**Notes:** {slide_content['notes']}")
                
                content_parts.append('\n'.join(slide_text_parts))
                markdown_parts.append('\n'.join(slide_md_parts))
            
            return ParseResult(
                content=f'\n{slide_separator}\n'.join(content_parts),
                markdown=f'\n{slide_separator}\n'.join(markdown_parts),
                metadata={
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'file_type': file_path.suffix.lower(),
                    'parser': 'python-pptx',
                    'slide_count': len(prs.slides),
                    'slide_info': slide_info,
                    'core_properties': self._extract_core_properties(prs)
                },
                success=True,
                parser_used='PptxParser'
            )
            
        except Exception as e:
            logger.error(f"python-pptx parsing failed for {file_path}: {e}")
            return ParseResult(
                content='',
                markdown='',
                metadata={
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'file_type': file_path.suffix.lower(),
                    'parser': 'python-pptx'
                },
                success=False,
                error=str(e),
                parser_used='PptxParser'
            )
    
    def _extract_slide_content(self, slide: Slide, include_notes: bool = True) -> Dict[str, Any]:
        """提取单个幻灯片内容"""
        content = {
            'title': '',
            'texts': [],
            'notes': ''
        }
        
        # 提取文本内容
        for shape in slide.shapes:
            if hasattr(shape, 'text') and shape.text.strip():
                text = shape.text.strip()
                
                # 尝试识别标题
                if not content['title'] and shape.shape_type == 1:  # 文本框
                    content['title'] = text
                else:
                    content['texts'].append(text)
        
        # 提取演讲者备注
        if include_notes and slide.has_notes_slide:
            notes_slide = slide.notes_slide
            for shape in notes_slide.shapes:
                if hasattr(shape, 'text') and shape.text.strip():
                    content['notes'] = shape.text.strip()
                    break
        
        return content
    
    def _extract_core_properties(self, prs: Presentation) -> Dict[str, Any]:
        """提取演示文稿核心属性"""
        props = prs.core_properties
        return {
            'title': props.title,
            'author': props.author,
            'subject': props.subject,
            'created': props.created.isoformat() if props.created else None,
            'modified': props.modified.isoformat() if props.modified else None,
        }
    
    def is_supported(self, file_path: Path) -> bool:
        """检查文件是否支持"""
        return file_path.suffix.lower() == '.pptx'
    
    @staticmethod
    def get_supported_formats():
        """获取支持的文件格式"""
        return ['.pptx']
