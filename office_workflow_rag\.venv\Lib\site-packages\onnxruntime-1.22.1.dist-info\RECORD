../../Scripts/onnxruntime_test.exe,sha256=TUbksnaQttMZ9CwLO672pcSLo2pLnDDk06FjRlbwAME,41060
onnxruntime-1.22.1.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
onnxruntime-1.22.1.dist-info/METADATA,sha256=51uuHbw-dmsK-dg69_omFFZD2h-bfMJotMyRimbnNQk,5075
onnxruntime-1.22.1.dist-info/RECORD,,
onnxruntime-1.22.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime-1.22.1.dist-info/WHEEL,sha256=qV0EIPljj1XC_vuSatRWjn02nZIz3N1t8jsZz7HBr2U,101
onnxruntime-1.22.1.dist-info/entry_points.txt,sha256=7qLS4FbGXwPZjfdpVAGpnmk9I6m6H5CxEnwcCx1Imjs,77
onnxruntime-1.22.1.dist-info/top_level.txt,sha256=zk_fJEekrTm9DLxX2LwGegokVqP6blqPhFoMIuh0Nv8,12
onnxruntime/LICENSE,sha256=wlDWJ48LR6ZDn7dZKwi1ilXrn1NapJodtjIRw_mCtnQ,1094
onnxruntime/Privacy.md,sha256=v7dxKwdfPwfj6-5dwqKW0d4y2_ca0oZj9z0VOMtsOwg,2490
onnxruntime/ThirdPartyNotices.txt,sha256=4A-Cjgoz3lkaNVrmYG0mJfV1jafSyETbeCHJ3T42R7Y,333022
onnxruntime/__init__.py,sha256=LdimA0CAD91wzB-0omsbC6Cg5Hv5f2g2KJwO4Ya0PbA,14344
onnxruntime/backend/__init__.py,sha256=5I1Ylsawf9w6MNmK4RiN1wA-EEQqlKKwYTNZB-m_k6M,334
onnxruntime/backend/backend.py,sha256=xPA69Lf7rwwwgeWoZ3CgB2JSoExuIAdhHmd6ROp19sc,8187
onnxruntime/backend/backend_rep.py,sha256=A7S4GqxLC6IfkbEXLlWiWpCD9AJ5x-xAhnR8BCM2cNk,1776
onnxruntime/capi/__init__.py,sha256=uRp4pMtfoayBhZgEsiFqFCD13Y6LUo82FdZsQX8X8LI,251
onnxruntime/capi/_ld_preload.py,sha256=li6cbZ64hDfUndat4mprUWzowLa3RQdw0q2E56sXFwE,413
onnxruntime/capi/_pybind_state.py,sha256=nbUpnUncwBv5pgJA8yugDYJRA4TTfC0gaYOED5jD-SA,1533
onnxruntime/capi/build_and_package_info.py,sha256=-sNXrXoC6HPmEMqKtQHjCvopBhgGb71zgfwIt-yWbY4,54
onnxruntime/capi/convert_npz_to_onnx_adapter.py,sha256=N0ShYr30vBQcOr9KyFd4AUdEcqWW89KVd80qSYCgdQ4,1581
onnxruntime/capi/onnxruntime.dll,sha256=Gyd9aVyMQbOnc2s_Mc4Q89T75DKImDqEAHkKCL10AHc,14854688
onnxruntime/capi/onnxruntime_collect_build_info.py,sha256=sD8Z2S15QHSvuO1j7tgqJKeORDUatwzUmzvC8Uj9cAM,2109
onnxruntime/capi/onnxruntime_inference_collection.py,sha256=rWHemDpK-2yU2bueRK-m6_iEdfrqzU41Os5_nfbA2W0,48294
onnxruntime/capi/onnxruntime_providers_shared.dll,sha256=q9VLrG4FniBrmYAyQs4ZZaF9x-adJPyrI453z6149iA,22072
onnxruntime/capi/onnxruntime_pybind11_state.pyd,sha256=VNw_yc8mh1RyCYvl-83N_Sqju9wUKAuKK6TfTlgk8uU,18104904
onnxruntime/capi/onnxruntime_validation.py,sha256=roKTGWetf5SjFDACO13ZlCVRShzPHsBzli5cFtNCz3k,6737
onnxruntime/capi/version_info.py,sha256=8mm1VTXF8xgx6N8vFNe0Tiik9qdg9Vvi9f32bPE9ktw,34
onnxruntime/datasets/__init__.py,sha256=DqRdpMfRtDfhVkCQu5lTmfSQ-GG4dETHNWdoB4fA7lU,473
onnxruntime/datasets/logreg_iris.onnx,sha256=giR4TJjXNBLZ_ZmrzVejhWi9WQmA0PvlkWRkUxxS6Pw,670
onnxruntime/datasets/mul_1.onnx,sha256=cfQxxOkyHsb76xWNAu0kBFmn3MmGc_p5pPQ5zkLvrxA,130
onnxruntime/datasets/sigmoid.onnx,sha256=U0Crpnp-NHUWKteUN4r1XxcY9V-aXXS0r2Dsx_emJLY,103
onnxruntime/quantization/CalTableFlatBuffers/KeyValue.py,sha256=e-jJFhw9fb775fDCLnWdbRSdoJ6vGD0c7qTnkIG-vNs,2250
onnxruntime/quantization/CalTableFlatBuffers/TrtTable.py,sha256=QQ9_f60Wya8U-KQOMu0gXImfhiPN6jNkfjpoCdAFic4,2665
onnxruntime/quantization/CalTableFlatBuffers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/quantization/__init__.py,sha256=gL1o5I5h988WNCfzJ1KMpSAvk2xjgw0HcqYsfXcKurw,647
onnxruntime/quantization/base_quantizer.py,sha256=VhzNK8QZO6i7aQr6ravD2KoSU-3z2ygjNt8m6PTQH0w,27952
onnxruntime/quantization/calibrate.py,sha256=pdmgFiPMMMGJzh6lF3zFWTpi5PCck5nwrSGWQMqxxYI,54014
onnxruntime/quantization/execution_providers/qnn/__init__.py,sha256=nKKB7VEbO574HDL2xdJPD8VeXoK2a3jd8nLBxULiVvI,120
onnxruntime/quantization/execution_providers/qnn/fusion_lpnorm.py,sha256=vUrbMNorHH7_uKjeL1jlkPghnplPIDPz0kmN0Tt03mc,5327
onnxruntime/quantization/execution_providers/qnn/mixed_precision_overrides_utils.py,sha256=PTdZPaP1cZAUXu14e4L0j71wO53lcD1s8yPkZlan5G0,18995
onnxruntime/quantization/execution_providers/qnn/preprocess.py,sha256=qLSdYvc18-u0h3CWaC8DKNWRmjdrb9Qup8nYk-DHiCM,14192
onnxruntime/quantization/execution_providers/qnn/quant_config.py,sha256=TjlRcNUw109mBWd_NfEUQn3UQLRt1B2dyT63g-LNba0,19835
onnxruntime/quantization/fusions/__init__.py,sha256=UMhvt6fL-eI4iadRoWpuFSktJRvNJjmGd5Rqw4nsFzY,163
onnxruntime/quantization/fusions/fusion.py,sha256=A6_77l5uw-hIwyoX7DPOFL6O-y3qXk-S16SMLv1Ncis,12088
onnxruntime/quantization/fusions/fusion_gelu.py,sha256=3qOO4U95ATD6S14dyC-5-vGeaQBr5U-GCsjfvHqoL98,10647
onnxruntime/quantization/fusions/fusion_layernorm.py,sha256=CKU--IH-xDUnm5qZtTK1ENYuBMnPsADUkzrOBjyW7kQ,5306
onnxruntime/quantization/matmul_bnb4_quantizer.py,sha256=lMe5mSbK__FKW14o-_6UdkeXOvkapMm2xV9YCJzVqto,9268
onnxruntime/quantization/matmul_nbits_quantizer.py,sha256=nd1kaUKXDQffvrb9YXj2JMtgcwgjt207K9JdrCWKiXA,64805
onnxruntime/quantization/onnx_model.py,sha256=M8NBT87QrUbPL4bZq_5ZMgfo_W4PEp3wM69ZAdvIRJU,24533
onnxruntime/quantization/onnx_quantizer.py,sha256=3FFFLNz2oDagpvpByz0kzy-QG0uEL0E6orrGt__zGjQ,43964
onnxruntime/quantization/operators/__init__.py,sha256=IfKXrFWtRSye1mkgD9lpwxio0fw9cVr_1CdV1cvefig,85
onnxruntime/quantization/operators/activation.py,sha256=G6XAKZaRIV3vrtjpK4kEvbzUSlLOwwEauXX9exgIHZg,4545
onnxruntime/quantization/operators/argmax.py,sha256=pfE9_eSTZ2otTkUcWwlLi7HJKtN10kE5c2Lz0SeVADQ,589
onnxruntime/quantization/operators/attention.py,sha256=eH7-Z3MfP6xRZCdhDAyNxWG2s2nZILxIEFVAHtqj7EQ,2637
onnxruntime/quantization/operators/base_operator.py,sha256=vrAVfKJXZvF7ZherKw4JUGonNyNuoU2TWnwBy-EQ3QE,1118
onnxruntime/quantization/operators/binary_op.py,sha256=pEQHRAS75EMp7LG6jzWV7gDQt_vzEPLJEI00eIOuoiA,2544
onnxruntime/quantization/operators/concat.py,sha256=fZFwnaqoOZ9b0ZvGpBK_MrJzVteeJguWRQ396kUh8QQ,2143
onnxruntime/quantization/operators/conv.py,sha256=whgjZwx8iWCUDhiGoMfZ8ADzj9lMzIamYrY2Ps7l8oU,10203
onnxruntime/quantization/operators/direct_q8.py,sha256=xWWLXoO1fJ0FXOFs0OdX5pvLVw3m7b6TBpU1YRYycO0,3389
onnxruntime/quantization/operators/embed_layernorm.py,sha256=2LsZk5Um0ELaRESWjScgYyQioJelRZK6oQbzAclSgXI,4058
onnxruntime/quantization/operators/gather.py,sha256=HL79Csv--zTAs5XmG1xP7Fjw72RTlqpadxB6w-OmqN8,2230
onnxruntime/quantization/operators/gavgpool.py,sha256=wYyjEf3h-_QChWKnsZ2N-haBG1RSvqRitZ-Yvfwo9Dk,2445
onnxruntime/quantization/operators/gemm.py,sha256=h7UW6xJNewLVE6lXsJdMX__hRhe_O2eyR-f7TrF18Z0,6241
onnxruntime/quantization/operators/lstm.py,sha256=gO3AqC3tvoiVlGRDKOLrBRPRyEfnC2EfazcMLOZ4AkE,5238
onnxruntime/quantization/operators/matmul.py,sha256=Z98R4saKkbQhps_jRCCJ91cmptxc8y7b5TmG3gvHIcw,8499
onnxruntime/quantization/operators/maxpool.py,sha256=QyDmHyBo0QKf6kNFbp2a9v6ThrBO-OL3tW0PFdN6bkI,961
onnxruntime/quantization/operators/norm.py,sha256=f3fUiSN4WdM8iuXCSjETorRyDFUSJJWxO10vegj_dEs,1649
onnxruntime/quantization/operators/pad.py,sha256=yM83jrZC88p5Fa53w3KLQc7zIlSz6Gv1iSOPvq5guM4,7951
onnxruntime/quantization/operators/pooling.py,sha256=L0IT7G6-2XSx9-wUz5BX59Mc43FfJEg79NwW3yqEDhI,2285
onnxruntime/quantization/operators/qdq_base_operator.py,sha256=Fco9JZxrXQoVgjKvmHFuzT0mogWo9-wHiDa51CjTioo,823
onnxruntime/quantization/operators/resize.py,sha256=BMeym-7GHOSnGpZisa9BkdQkVmCXwKANA5NpnKRnaLI,962
onnxruntime/quantization/operators/softmax.py,sha256=e3ThVOh2TG1B8luG6xWkoT_hCdvsMRjvTlTje8CW-YQ,2714
onnxruntime/quantization/operators/split.py,sha256=82R65-_Rw5g23f0uekUWpA3nhOzeUWdOQgr2JZXwrOc,2258
onnxruntime/quantization/operators/where.py,sha256=wd6PQ7LlbrJTqamFMch_Fipnbt4IewMJSAPozMTrwKI,3127
onnxruntime/quantization/preprocess.py,sha256=VU4iX7g8gOgVH0zehOcOXsVWkZpx6kG_LFlwGM3Bs6c,5045
onnxruntime/quantization/qdq_loss_debug.py,sha256=1L-BuA1Jck9J5lgRoj7QPEbtaKHMkoQLMTD4iYFOaxo,15829
onnxruntime/quantization/qdq_quantizer.py,sha256=4UiRF83h_G6TIwe7jVuyDJ5gmugK7fV2ZYs79EEk7i0,71380
onnxruntime/quantization/quant_utils.py,sha256=gVjTrwkmZ0ElYU8TBPrFJqiBhAmR6___cyfsw1SE6_M,39920
onnxruntime/quantization/quantize.py,sha256=Hs_BPZNVGGlU-Ub_cybUj_dAKudZSmA0fzwmO9uieWI,53364
onnxruntime/quantization/registry.py,sha256=oORuxmnecCmS8x0mLtkYalz6sqVglGeX0--R8Yli7_c,3794
onnxruntime/quantization/shape_inference.py,sha256=VbTYK38XllUXJL3ziitrUoFYpvAEAK0644HvXfwRHxw,9093
onnxruntime/quantization/static_quantize_runner.py,sha256=SA3B1PrpfUBZeKZC9swAsuKEBTgH_RvOBf8-6UYwpp4,11315
onnxruntime/quantization/tensor_quant_overrides.py,sha256=RJa3YIUFpyfXiMeuBsoK5n-ZfDzhIaSeEjLW3Kt7CJk,21287
onnxruntime/tools/__init__.py,sha256=7up7iKcklVy6UcpIIIIlBaK690O32vaOxyaaTWvwyxU,528
onnxruntime/tools/check_onnx_model_mobile_usability.py,sha256=hzqjrI9Xz7LP1idPdviRU7EEeLMvUB0qlyTqHofxlFs,1717
onnxruntime/tools/convert_onnx_models_to_ort.py,sha256=I_9nhCipOpgsX5dNovCOaY3bc6NyfQLE7KuPKMzucM8,16810
onnxruntime/tools/file_utils.py,sha256=HrNK4UjpR-u43tLBC2vwKieisCVRBSFnaIDKaswwEGI,1572
onnxruntime/tools/logger.py,sha256=s3M5-Akb69zubXNhCpsjIoJ052gYieHV5FsOfBZ6lrI,333
onnxruntime/tools/make_dynamic_shape_fixed.py,sha256=28zdPjT8yz8AEv5hkFl_YNI8qQzbmP64hl-GqXO-Or0,2642
onnxruntime/tools/mobile_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/mobile_helpers/coreml_supported_mlprogram_ops.md,sha256=cx6qWbd1NI0wZigfIVOi-TKziF4_Crx3bm7zgSrJZqA,2403
onnxruntime/tools/mobile_helpers/coreml_supported_neuralnetwork_ops.md,sha256=JJBwv04C_o2K1UWOttDtarnC7xFFZQ2uvvIZg4EFmWA,1958
onnxruntime/tools/mobile_helpers/nnapi_supported_ops.md,sha256=uJznEyy7ZAdlrkKQeoWFFs55rPE-kOePIJiv741r98Q,2385
onnxruntime/tools/mobile_helpers/usability_checker.py,sha256=JTkcd6gfEPWYQSrz7ajSDgvFxFc0rG6w3L-vuk_iZLo,32361
onnxruntime/tools/offline_tuning.py,sha256=IswzPfMIbWwZKBBX4pat8zu4s1D5sUzVhLjDmWzQlkI,6368
onnxruntime/tools/onnx_model_utils.py,sha256=QYZx-7BVQ53obLSMtVwYFsHJtTQSGSgs6fI8i0sKYDY,16686
onnxruntime/tools/onnx_randomizer.py,sha256=9L96dzIf59cQ2oQsmR2EEsdrR4hHwEGrpZkajEgUPAY,3361
onnxruntime/tools/onnxruntime_test.py,sha256=SvqgwrjiIpf_vsZfHmkE_FPXJkDA18mZpwYoyjMv5g0,5770
onnxruntime/tools/optimize_onnx_model.py,sha256=vFBfBMOP7XcJnzQhT6DtDG5QgoE0reQ6UalUusoSv1c,2005
onnxruntime/tools/ort_format_model/__init__.py,sha256=Xsq0LM-zEkNsULbTTvyxnz4dDzqepeE8BEgHzJGYOD8,1307
onnxruntime/tools/ort_format_model/operator_type_usage_processors.py,sha256=rl9Ub_QRC5B0F9Mu0LbY86bsVnkUf7XYn1PtwC1NPNM,27032
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgType.py,sha256=ErRXrmwza1xgVW4OAENw_B8yVc7WfWbAYYhiAO3Cc2g,147
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgTypeAndIndex.py,sha256=8Sx0mmUqjN4uN7MeTY6scIgeZO6gruARh8MvlEQdUdQ,2093
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Attribute.py,sha256=S5SA5y9FB2HWcPIPx99jz_ebTV85O8cTETJgGe_FKLo,11187
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/AttributeType.py,sha256=piF8u_U7zNE5eSrwcQhRLgvr2axK21-455McxIje2HQ,346
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Checkpoint.py,sha256=0cZ14luQy_w8GlbDF_xoGPWtee2UueQM4jt59YX5B9o,4342
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedKernelCreateInfos.py,sha256=K6Sl9lPxPE8huGE0tunG9MXDFYbzJs5Hc4nzKj8ft88,4648
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedNodeIndexAndKernelDefHash.py,sha256=Lr3cs1e80M1zLbZ1hBKwDsSl6d8zxjkbIfAguiTkJko,2526
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSessionState.py,sha256=N5CN72wJObCmYGcbA5z9o3u1khYxJ9bVSIaFIJCDR3E,3678
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSubGraphSessionState.py,sha256=ARjFKPJX_pB2KTrSUCHz_iN_UmrB3xRDKtUbIepTX-Q,2682
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Dimension.py,sha256=VHQcCV5ip0BIhm1EFtsAXA-uMp8tqlu1BfiJP2LZuQc,2262
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValue.py,sha256=14W1geQj5_V_5UiqgrG2oFGDgCkAIS5d71xemZwmO_Y,2574
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValueType.py,sha256=2kmuPePZe_rWSMkI7ceIhewHL3wvvsDEhzH-LBusm0M,174
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/EdgeEnd.py,sha256=aNdY1SA8fDyvH1Vsqo5xklVuhGtw2sUmyy86HmtWEbk,1137
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/FloatProperty.py,sha256=qqpRpfy9QXs7WHhHUARUjv5q_zs8wiT2-iOjKD9v4NQ,2075
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Graph.py,sha256=Y_AnjssgrJ7o4oU1w-KG-rOl8EE8YuEnvDWu7rGN57E,11039
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/InferenceSession.py,sha256=0FgbOyXfWicKshG131GzHqPTFJvoVorBxTVP1xM6ZNU,3125
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/IntProperty.py,sha256=EjWio-yFwfkE_N764bYTaDRz0EHSZ1n0JePkLSNGitk,2037
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrArgsEntry.py,sha256=lKy6VMEjvkiv5BGOjugWEZtlobNMwvlfcSRSYzjCiAk,3193
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrResolver.py,sha256=cufEs5uTENEF6bvZQemzDztbErNtvDuzMpDoCnSBpik,2867
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/MapType.py,sha256=wlC76nL8dC0vdZVlEoUC9qwgbVDwnSt5RCRO5r_Wfmg,2194
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Model.py,sha256=CPbe2q2OcKU8LbShHGtnIG_jrW83f3nvMR5Trg1Nx90,7663
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ModuleState.py,sha256=rGMagBBqyCNbewI-xUygxN07m7fLDQmH2EWISmFd7ss,4994
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Node.py,sha256=IjM4YjGq_swHTiLw01xsSWmAYaZmkVZUR3cei3byY68,10718
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeEdge.py,sha256=LiB9vyQTsFkfRRTuiDcwrRfVaWrRzAALP5Rop0rtGKg,4183
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeType.py,sha256=bBLbvDWziLLRxhjCAqlS44k_hdIWbWk3vsRxCDOLz7k,151
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodesToOptimizeIndices.py,sha256=KoWmACgRnK8qGNyXiKo6NywUvzpvYYxGQ0AyayjyaFQ,6144
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OpIdKernelTypeStrArgsEntry.py,sha256=6WV2Pj3vkqJ2NZ19VWrPAxKtoGCeiJ6NTVOvs89BIQ4,3387
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OperatorSetId.py,sha256=UOiU-CCvexY3OOWk2hiZyBI0OItcA6jiZTsERDZxu2U,2099
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OptimizerGroup.py,sha256=Xri5OFTQ881fzaVdQZjVviLyE5vH2aFGGArQE5H1ZbU,4135
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ParameterOptimizerState.py,sha256=AIEaCRcEln5wC7X3C8vp7TmmpxuaH7t93UCphxbxRJA,3218
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/PropertyBag.py,sha256=QXs064QNug8pG9zjX9aWEPmO63CIWJcL-kMLgMPk7vI,5099
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecord.py,sha256=Lrl5lCzbUL323Y1nnij-7vn95d3qNHkiVPwkbg6SSds,4067
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecordContainerEntry.py,sha256=PM7_R0kbnF3K32qoLOIPXmeudQfj0pvabMQLB-FjREw,3832
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizations.py,sha256=qYkC65wMqu7FinW2X9pPTeRD4bY7xdIsOfXnIJoFDFU,2800
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SequenceType.py,sha256=iiMOzyjzXzjO3zoad_oG2-ct786Wv8u4LWoFOZdmc0A,1829
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Shape.py,sha256=4Y6yU35JHSXKzb2BDzObkss4yiopwS3IXkSBpFf88FI,2352
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SparseTensor.py,sha256=3EZahzlyO_73WOzK9jZHwoVqsQkq3407ATBOG56KLEE,3806
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringProperty.py,sha256=Zd5QCcSgvf5ojuOBRV19gGyQPhqjJkoBaMdZ06kOc7c,2110
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringStringEntry.py,sha256=Iih7xRq4zvu4cVV4_TAC4sbjaH-dLqZGU68X0oLaWoI,2147
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Tensor.py,sha256=CZItxbQ-B9cJnaS5HwBNWT1buat_JgX-W-zOn9m11a4,6802
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorDataType.py,sha256=WmblM8GoLES62L12Ea3dC655_ruO2T3R2Q085OLCFc8,500
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorTypeAndShape.py,sha256=noUmY1JdkO-Bjhghyuevf7cy0Gokhnh0r_AcmIt1fl4,2326
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfo.py,sha256=ToFYZzH0Vit5VVIztodfzP-fCOc97vgBHXz6Y_tHyWk,2599
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfoValue.py,sha256=a2VKzgcR6v4qiffa5a5HYNUaHKDLdmsFbAdsNwrmT5Q,198
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ValueInfo.py,sha256=ZC6peiqYRYKYw_si4BmtZUbRRLf9BkkD4za4xky3QHY,2655
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__init__.py,sha256=EfkIrreUF6TrcYpBo1NJ8GOV_p_o_YXg3fSptBN5XUo,251
onnxruntime/tools/ort_format_model/ort_model_processor.py,sha256=Zh07RmcGYf0-qbXRiim2DWUGHbsrEeG6_rOcx3VLexw,4472
onnxruntime/tools/ort_format_model/types.py,sha256=8r38Y7ghaGn6_4muEWSpeDHkY3rsIwAB76t5YD8uMFk,4468
onnxruntime/tools/ort_format_model/utils.py,sha256=Ix5mFXZCnMEHf8Tg7Mwg2GFdy0d1l-zocT2fsE8_8sU,2604
onnxruntime/tools/pytorch_export_contrib_ops.py,sha256=qqOOgFh50uFNVHeOTvya8YXAMkBaKdRPDoQWVjZprwI,4892
onnxruntime/tools/pytorch_export_helpers.py,sha256=MRegHn3z3VhVbZQ4O-kTGedIE-pufyxhq1A1GVIdCjY,5971
onnxruntime/tools/qdq_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/qdq_helpers/optimize_qdq_model.py,sha256=9kpU0Dukc0ZEHnjwO7NqNFUFVRtOx8xgFPVWZpXkEcQ,1279
onnxruntime/tools/reduced_build_config_parser.py,sha256=F9bN5_yi5V7nLQUS2vsfo7gbYB1cZnSAQiUTZOdvqM8,10161
onnxruntime/tools/symbolic_shape_infer.py,sha256=vXtur9JEiFVGRgkyzz-ksoELpog97VFBE6sxjOIuhtY,145642
onnxruntime/tools/update_onnx_opset.py,sha256=fplb1ypV-pFhu8Xsi5u_bDfI7EsC4zamJkTziccgQ2c,1182
onnxruntime/transformers/__init__.py,sha256=2c213CqzXrc0N6Cqf__Te5d_SH_stfLdNdeNrugB7SQ,321
onnxruntime/transformers/affinity_helper.py,sha256=KOKBvzoBr-wOk0QHMGKzY1uy1iI7E6eHpYwBdTHM-Y4,1442
onnxruntime/transformers/benchmark.py,sha256=ovALO57Ri0_x8PQ-qDk42Py2MiPBpqLi4W2DNAK30cw,33682
onnxruntime/transformers/benchmark_helper.py,sha256=29QnYKBKf4ensjuIThn2hTT4pItV0rtglmHkPXrLN08,23141
onnxruntime/transformers/bert_perf_test.py,sha256=V0fNGZTC9BSnnYAT3UqVhAqs0UwDfvAWQfGOs-_JVgY,20954
onnxruntime/transformers/bert_test_data.py,sha256=JiKzB6ugOpxcxlOAZTb_jrVAFf3GNeeFNygphycMdvc,23429
onnxruntime/transformers/compare_bert_results.py,sha256=BqCo9QjNDTA6roWOZZoNlMB7JNLnk_RQL1HQWnS64AQ,8431
onnxruntime/transformers/constants.py,sha256=HBmbfaRLrLCen1q0C9tZYJApn2NBPX2p2h05hc5DTsE,1127
onnxruntime/transformers/convert_generation.py,sha256=hlXbVZFiPMZ3FF5smwkCKTRsBeXxhHlvZPudTqpqDAw,142529
onnxruntime/transformers/convert_tf_models_to_pytorch.py,sha256=JrMGzUi_4cMfYoIYrxa1n0jnMDG-WYj-xmUXZmH8aJ0,6705
onnxruntime/transformers/convert_to_packing_mode.py,sha256=2HlOMlu-PXx5Y-JPJaWsU4na7UcA9bqvKR-3-IPGjks,16792
onnxruntime/transformers/dynamo_onnx_helper.py,sha256=Q2wGVH0IQep-ZwO5JkTpgPWwzfKMMbnn6q2skvjTozY,7785
onnxruntime/transformers/float16.py,sha256=nMTYghlnKLfqnnAVnsUYCKEwt_nkcPe3T9JIfg4Un3A,24687
onnxruntime/transformers/fusion_attention.py,sha256=alxJuJsQNinfhoIjGkUKt_eJ7Y7Nuwz-z7Er4X2hg4k,51393
onnxruntime/transformers/fusion_attention_clip.py,sha256=7K7VhmIDV-5LKduHj7oWcqYSO5t8HaAP1SIEJwIVCII,13765
onnxruntime/transformers/fusion_attention_sam2.py,sha256=s5TNIr5hi8UiorlfAzEAlJKUfcn6WhEvw5CEG1bH3Pg,21307
onnxruntime/transformers/fusion_attention_unet.py,sha256=CAQikTpvz4KbCJUx9jonZzgy30l_w9ul6hW1RNnXUtw,56955
onnxruntime/transformers/fusion_attention_vae.py,sha256=1JwY-DnsngQymXCIhb_huu3SzvgXcuKDkfC7duSlfgY,12379
onnxruntime/transformers/fusion_bart_attention.py,sha256=lzb9Siw7QDdshhRopjqi54Z2pNFWa9uEwZQqWjI84nE,29990
onnxruntime/transformers/fusion_base.py,sha256=TOduoZTiSfrh-2QyM7F7YWb1HpJnA9uOGY0IwHFl1yo,5978
onnxruntime/transformers/fusion_bias_add.py,sha256=YOOZSfui3NOV0GUcNJmRSz-hoZOjQnRimjfAZPNOTzM,2041
onnxruntime/transformers/fusion_biasgelu.py,sha256=vGamxthOu6jXsxCRVdTFaP25-_tnjz9TVq91pIRV_Is,2300
onnxruntime/transformers/fusion_biassplitgelu.py,sha256=qN_YFjwT8jmBUaDobT9eGRZDCT6vSlbJvD0ZStgNFJs,4491
onnxruntime/transformers/fusion_conformer_attention.py,sha256=G8fq5BVB6t8--8xCpr1_tKmD6Vf1kw9UncCKB7CxkKk,8424
onnxruntime/transformers/fusion_constant_fold.py,sha256=qkthyYCZlSl1RMvfncCCmfkDzCsZ4tlOE9kf1-qENOA,6014
onnxruntime/transformers/fusion_embedlayer.py,sha256=RxQ2ZhT96d2g7-m9QcRAj4mD63Dn2s1Zz2J59AykKp8,36674
onnxruntime/transformers/fusion_fastgelu.py,sha256=RwvKIBJGRMk2MoH9j6HtFfQByH_Hw6eGmjeX9dtx_Lg,18181
onnxruntime/transformers/fusion_gelu.py,sha256=S9Jia08I3jXwafbo73xrqPOCT7rNHCnpASjZLHWiRcU,10451
onnxruntime/transformers/fusion_gelu_approximation.py,sha256=Xsa2v5mHjEuZrwnf1bm3UCCJ8I1is0dmuzzXgf5zDl4,1029
onnxruntime/transformers/fusion_gemmfastgelu.py,sha256=iRdK6jX-LlUIBGxjGavJsZKgLrj-hPBXpLvA13UIoR4,4208
onnxruntime/transformers/fusion_gpt_attention.py,sha256=20ZhplkAVJ3rq1VWwcNRmRs6OZu7lTHKIop3SAyDSUw,22508
onnxruntime/transformers/fusion_gpt_attention_megatron.py,sha256=HhoweTBxleb1niPOU_cfQzvUwM4LjxCVuZZWVEy3Imw,13639
onnxruntime/transformers/fusion_gpt_attention_no_past.py,sha256=qQb8WekiDJeQUV8egoCTrLoZki018veZTVVE-w3p3ds,10794
onnxruntime/transformers/fusion_group_norm.py,sha256=NBilP_TOZjlhuUS81EEEZMDNDzRPvje_r4_VTx9YlJ4,7645
onnxruntime/transformers/fusion_layernorm.py,sha256=qWb5Zp7itAXFyGeH4yBRr_XSvMZt1iTukBi_uzWWReU,20845
onnxruntime/transformers/fusion_mha_mmdit.py,sha256=l6KDhrKj0-9Q-t7hCYcWxzzkUUuPu6HFUPaPJZ_GwT4,25816
onnxruntime/transformers/fusion_nhwc_conv.py,sha256=yDNTzA_iIBYAc7_OLjZiFt9UTsRNWu7IAOo20ykfpLQ,3948
onnxruntime/transformers/fusion_options.py,sha256=NSeQVc9Hz9S-4uGpxuYfsigzdLDOdsRqXyujbfRPGhw,12704
onnxruntime/transformers/fusion_qordered_attention.py,sha256=s1BQ8OolahrgxaSriVmli2AqTmn_fNQZH2KxU1HhmRM,17137
onnxruntime/transformers/fusion_qordered_gelu.py,sha256=4nuNJ0W9mDKH9Uw3tuEOc8O_ld2iRLA-8t0CUaPx7IE,4410
onnxruntime/transformers/fusion_qordered_layernorm.py,sha256=hzWVzxKVlL_fYd4Nv-xlOb9AYSQjHY_j3HzkFv-p_XI,4932
onnxruntime/transformers/fusion_qordered_matmul.py,sha256=5mjdEPTb8aT_ajAZFqEynM6fXWjJmg4iBpz3MrR-ayY,8541
onnxruntime/transformers/fusion_quickgelu.py,sha256=e7MMq9G2aiIotoxnkd0a7-XmvyY-wYyefdTmXtBO_f8,2869
onnxruntime/transformers/fusion_reshape.py,sha256=AfT88v22G6PgZPzVKM39_QduUnlIbe8dbxvPCh-5dkg,6403
onnxruntime/transformers/fusion_rotary_attention.py,sha256=W0qhTvG7_AZdfpUZsujGYBY7KVJenqgQ5n46Iz-zeh8,68222
onnxruntime/transformers/fusion_shape.py,sha256=SbwDCkucLt3U-kCdxEEaJAJa4nWRtKdU81qHYYcytfk,3763
onnxruntime/transformers/fusion_simplified_layernorm.py,sha256=RovL8O0nVTMZTWgvXQ-vfzH_1k9fd0hDY70oKAu0Ly4,7831
onnxruntime/transformers/fusion_skip_group_norm.py,sha256=_dK_FotisHnc4s0zuyAzYNjHBznh_0iGWHuSD6BgGzo,10855
onnxruntime/transformers/fusion_skiplayernorm.py,sha256=P4r7cXVTjRLZIyOqkOXl5gUjiOBiXRQaaV7u4Xm__I0,9168
onnxruntime/transformers/fusion_transpose.py,sha256=OkcS-03fQuIau8MmSXxuXyUIZqlfp7xYkE_DyuzOneM,7004
onnxruntime/transformers/fusion_utils.py,sha256=rEMNm7hHFf3t3ZAS9dsQ2q3Z0QYtztao7U0GH3HEY5k,13177
onnxruntime/transformers/huggingface_models.py,sha256=9X08Ad57xm-GiPcB6YcHRJT10QBE-DmGUL32xkP3OIU,4005
onnxruntime/transformers/import_utils.py,sha256=_ILscQRcSyaJHt1l6jqcny5FWy7Qr6N_7hKs5aav8oM,651
onnxruntime/transformers/io_binding_helper.py,sha256=B4-InUgFmE1W61xKG6K2NSi8DEw-GGYEWnZTwAVXfUY,17552
onnxruntime/transformers/large_model_exporter.py,sha256=f9VswBG1yIu_z6I4OfU6rYefIA2zladP9INzeo2Z-c8,15296
onnxruntime/transformers/machine_info.py,sha256=mspwHbo8BXh0gnSNNFFDKam2rDvPN_Hwal9QQDRqe6M,7453
onnxruntime/transformers/metrics.py,sha256=DEjoDQGAf2Yq_BAz66x3w0KZm1QhvG4ZENk8rIIC2UQ,5223
onnxruntime/transformers/models/bart/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/bart/export.py,sha256=PNlhkbvrxTxSSLXpzqoa02Lektzf8rdZpcVFBxw-qcI,4285
onnxruntime/transformers/models/bert/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/bert/eval_squad.py,sha256=z9Ho5WZ9axsXXwM7PIKqlM1uQGjtbQrffOZp13-7jMo,12353
onnxruntime/transformers/models/gpt2/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/gpt2/benchmark_gpt2.py,sha256=53krYHNeJBk8ZixWoWrZHxCbe9z4bDYiGW02tzxR-88,15930
onnxruntime/transformers/models/gpt2/convert_to_onnx.py,sha256=pqz7enfiU8ZXxbghKekL2H-ItDb_WW3rSu52K8FGTEk,20618
onnxruntime/transformers/models/gpt2/gpt2_helper.py,sha256=1mZeKeDKm5jXgVg2sE5Jofbyg_0HQ84d9Gqg26dLPes,41331
onnxruntime/transformers/models/gpt2/gpt2_parity.py,sha256=w7hI5FOKGDf7KCVtjBmcyC7Pv986fjzfOibLV60w-wM,18231
onnxruntime/transformers/models/gpt2/gpt2_tester.py,sha256=y85y46HTQ_rKgEx1sdZF5xD627cSTkEegq_ys8XT3cE,20020
onnxruntime/transformers/models/gpt2/parity_check_helper.py,sha256=jU3bTPvyKgHqxrGIce12_LbqaXC688XnBnBp5AHz_ZM,5806
onnxruntime/transformers/models/llama/__init__.py,sha256=yR2FucNw-jt_3CbNt-zuM7DmldPq1rJK3SV8gRISzN0,490
onnxruntime/transformers/models/llama/benchmark.py,sha256=NI08C5ghVJQenbYw8sEUJ7L8snbNB9_6aGdsyttjLYs,27488
onnxruntime/transformers/models/llama/benchmark_all.py,sha256=7GeipIci1sIVLxwv9pucwnRp2Mfqv-kqaCQbx1CaA8U,15767
onnxruntime/transformers/models/llama/benchmark_e2e.py,sha256=741bV8ZSqA0CmP5quL3gMuIXYp3hwBoihPrsqPEI2Po,25477
onnxruntime/transformers/models/llama/convert_to_onnx.py,sha256=K5wBiCO-zU5mEwGtyK9SX5ylfyrCxgBtJBq7ePsJR2I,44816
onnxruntime/transformers/models/llama/dist_settings.py,sha256=4rLhv9WYMsAeSYTlLJfvGn3BlnUoXSGSEh3ORCmgpgc,1636
onnxruntime/transformers/models/llama/llama_inputs.py,sha256=x7vxfIAMA0QmMVtjDVNSOoA8nrKv22bTnRNQfSyK1YY,20726
onnxruntime/transformers/models/llama/llama_parity.py,sha256=wrXDczrC_ltEm-afUpvw6k1QkSefNsE-5wPF3S5MZAE,11818
onnxruntime/transformers/models/llama/llama_torch.py,sha256=OIbSpg7bjfPN9SzyGfaBKmzsa-bGHQTx0X-etiEl0Zk,1732
onnxruntime/transformers/models/llama/quant_kv_dataloader.py,sha256=piVldpGm9eBmF4wzgmKJprhujqTPddqORxZyLizcJdA,4959
onnxruntime/transformers/models/longformer/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/longformer/benchmark_longformer.py,sha256=p0yVWR5_XvN1ilI5JDf20WOFtl_w856dAqE-yESfh8M,30238
onnxruntime/transformers/models/longformer/convert_to_onnx.py,sha256=cTmSpSZhytBrM40Ys1r4FCUctyovXS3_e40_iozD4Bk,15219
onnxruntime/transformers/models/longformer/generate_test_data.py,sha256=wQxpgo_vZBhKRlquJwUB9FH3_xxvyDC3aCCZdkvADLM,9964
onnxruntime/transformers/models/longformer/longformer_helper.py,sha256=n3crEYnVYZxNzoyTNMzX3LFgxHH-FcaqtdfRmyAMg-4,3123
onnxruntime/transformers/models/phi2/__init__.py,sha256=yR2FucNw-jt_3CbNt-zuM7DmldPq1rJK3SV8gRISzN0,490
onnxruntime/transformers/models/phi2/convert_to_onnx.py,sha256=FygJZ26d856VcrhWrfjBc-TEUj59viNvvX0SWeCldk0,20640
onnxruntime/transformers/models/phi2/inference_example.py,sha256=xZBHx3iFJ8jQvvNxFZTdYAFLxdOPYcldmk53ktgcjew,17700
onnxruntime/transformers/models/sam2/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/sam2/benchmark_sam2.py,sha256=yt02GY4QgPvHeGpMcJaUeplBy3FcXNn9WWJD_AdD-Q8,22366
onnxruntime/transformers/models/sam2/convert_to_onnx.py,sha256=jtqBVixfO2ztpxb3-7KyLtOJ6Jc-1Iwlz0yPgGn4laM,10652
onnxruntime/transformers/models/sam2/image_decoder.py,sha256=R2GmRGpf51-MKDi-g9O0VewCUwCRK_EKiEWlR24yzVU,11059
onnxruntime/transformers/models/sam2/image_encoder.py,sha256=CUdeP343SPKqksCg2gVe9WRXPkEa_EVNPlmMyqmWrOE,9720
onnxruntime/transformers/models/sam2/mask_decoder.py,sha256=7Kcb98z6qs10a675IRM4Aw_LATnnplVbC9W9V99eIxo,9049
onnxruntime/transformers/models/sam2/nvtx_helper.py,sha256=XJFujDb-p27zCJs-O-ZjcKEAetc5dc0keFvsIYgFQ6E,1312
onnxruntime/transformers/models/sam2/prompt_encoder.py,sha256=wG35OOCLpQ0BQG5MymQQafBd8Fg41AdoaO2EIoZ9m78,8513
onnxruntime/transformers/models/sam2/sam2_demo.py,sha256=s-oQw6_1BBH3OuDtjya3uPUXR8CfpJSJHolc0nI_xTc,10805
onnxruntime/transformers/models/sam2/sam2_image_onnx_predictor.py,sha256=bB-f7bi7NSb0XVeAA6J3v9jkMMJDHdHmS5PdX1UO0nU,12702
onnxruntime/transformers/models/sam2/sam2_utils.py,sha256=gNzqfMwdmIbkWCvRrKeJ1uYpz9fqFxrGNGQ9ikMxRfc,5667
onnxruntime/transformers/models/stable_diffusion/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/stable_diffusion/benchmark.py,sha256=ezuHwTJ8P-3gWU9JRieySdMMRr99PeDEUWQi5INfWK4,51139
onnxruntime/transformers/models/stable_diffusion/benchmark_controlnet.py,sha256=0wLUcJMeSzrDOynjT30wC-8ncukGmgEnv5ngv9rFNk0,13253
onnxruntime/transformers/models/stable_diffusion/demo_txt2img.py,sha256=k_H66rQBSryX5MvHzheAcKq5pXIMPeoktzxP-EKfrPw,3394
onnxruntime/transformers/models/stable_diffusion/demo_txt2img_xl.py,sha256=F1DGjYE-kDuYdBn9ZMPT5Lc7jJNsCs_ZkTT5JgcGmD8,10179
onnxruntime/transformers/models/stable_diffusion/demo_utils.py,sha256=iGGq8J1y7IVt_edTat08CraS81iSP_cjuA9C4ABP7Nc,29342
onnxruntime/transformers/models/stable_diffusion/diffusion_models.py,sha256=SwAEjw6mI08qNxpr6f8xfeg_TrhafUeLmSSkfePWuUs,51716
onnxruntime/transformers/models/stable_diffusion/diffusion_schedulers.py,sha256=ohAEuJNU_-rgz9wwz7pRc7tJI1QJOc5zjJYkvuh2jKs,49468
onnxruntime/transformers/models/stable_diffusion/engine_builder.py,sha256=87fTomCAw4rWeIjD6m2b8bBkQps5EglNBFd9mDSO6cs,11948
onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_cuda.py,sha256=t9cjyv617MpQt_S7crnspAHbw0WT8Rf3lCVRkeLwuVo,16241
onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_trt.py,sha256=0M-JLT3Z1zYPEVkJ0TPCZuhbIFCstbBi5Wh623VLcww,11451
onnxruntime/transformers/models/stable_diffusion/engine_builder_tensorrt.py,sha256=LSoAcwy4nwFQ4aikwJimlAQl1iXAWX4nIbcWD2H7qPw,15999
onnxruntime/transformers/models/stable_diffusion/engine_builder_torch.py,sha256=4SeIgxcd7Vv3WuSpKcklESud8-O6tZKDVpFssNCzUTg,4289
onnxruntime/transformers/models/stable_diffusion/optimize_pipeline.py,sha256=s_Waws4h36hNomETPrJSLgIBMPNMPqhCOF1Z3soOK_g,22076
onnxruntime/transformers/models/stable_diffusion/ort_optimizer.py,sha256=Xi35IWxLtzTcXBhffGeOx2ltV_xCsjAzz7BBwi78mDE,5836
onnxruntime/transformers/models/stable_diffusion/pipeline_stable_diffusion.py,sha256=SlF8-VZYKufhpoT-oF5q6eaxdR9ODqdR9SvPMzIp6dg,33961
onnxruntime/transformers/models/stable_diffusion/trt_utilities.py,sha256=XZCfqG_kZ72e-L9p7PlGqc4NLvFZF1h40A6Guyj6z8k,432
onnxruntime/transformers/models/t5/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/t5/convert_to_onnx.py,sha256=70tYNOGfx0HgIHgMdbRTLbsJlhQkEbW4h2xZveSvNeE,10510
onnxruntime/transformers/models/t5/t5_decoder.py,sha256=GmUTeBavhX2TfIjZIfMNuMML_3-6BRqetUEXAvOxFPo,17188
onnxruntime/transformers/models/t5/t5_encoder.py,sha256=xSjy9AmscMdyCDzSn-v59xXqS634lAs1EmsJUsBaB0E,2319
onnxruntime/transformers/models/t5/t5_encoder_decoder_init.py,sha256=hf7ozFvgXSE_FH17p88MS0fpPBbnlsUYfKgncCkmnvU,15421
onnxruntime/transformers/models/t5/t5_helper.py,sha256=UTbwVMVZeHJEFUs8ThxTMDaRj71fecnHiY-3u8NdPVE,12481
onnxruntime/transformers/models/whisper/__init__.py,sha256=yR2FucNw-jt_3CbNt-zuM7DmldPq1rJK3SV8gRISzN0,490
onnxruntime/transformers/models/whisper/benchmark.py,sha256=Qy4NcHoo5KG_QVmFl2V8_h-F-P2qawlqMOe3HEk33M0,23356
onnxruntime/transformers/models/whisper/benchmark_all.py,sha256=vXt8ZwBxQVqjy1u2wmLe4vX7EaJsjejx0_aFtaRWkGs,19334
onnxruntime/transformers/models/whisper/convert_to_onnx.py,sha256=iUeeCjaXX34Kh3-fJ2dHgONGsQicMVOoBUEgDG89umo,18938
onnxruntime/transformers/models/whisper/whisper_chain.py,sha256=vCPpFzghlLsyMPBwemLh6zWSE1qO9EfbozZH5ywrgJg,15230
onnxruntime/transformers/models/whisper/whisper_decoder.py,sha256=gsaMRbPMEHlsbcBZZSwSf7KUx3MbUGp4gKZ42SV6fmg,21898
onnxruntime/transformers/models/whisper/whisper_encoder.py,sha256=vyykcthAejmd2K4lJyqd3JxXxTwcOVhxqMdj7AAeyUU,6342
onnxruntime/transformers/models/whisper/whisper_encoder_decoder_init.py,sha256=WZUKnFbMo_ZYZLvlqN7PmidhA8kRL2IzknQvaEpIGGo,16808
onnxruntime/transformers/models/whisper/whisper_helper.py,sha256=43VuIfTaFEPlxyLeEcz6gDT4YiKBgPG3Y3njUUhlXjo,21859
onnxruntime/transformers/models/whisper/whisper_inputs.py,sha256=m5cu_FxmQh1KLu5apcfL0LcfTlrAW6jkOgoFu11bk0c,16050
onnxruntime/transformers/models/whisper/whisper_jump_times.py,sha256=d9bgTRwP1evQSTu4b9mzlCWPfKZP3E_cvZaBdHYgvpw,19953
onnxruntime/transformers/onnx_exporter.py,sha256=RApGm4mmZ8HnustudUzad2GU1iT4XvbvNCWu6IYnd_s,25170
onnxruntime/transformers/onnx_model.py,sha256=0a193EyQtVinNHA3-yzSe6Ako8trMZZDLNSUJG13cQk,69441
onnxruntime/transformers/onnx_model_bart.py,sha256=yLPRW-4YuZ_H_6xN5H4hE2Wr1bYp7Em4ZrP_T0hC20s,5547
onnxruntime/transformers/onnx_model_bert.py,sha256=4UF9Omcq2cKQ4VeycaH9olH8h4RvlEQUsrkuR3qXG3Q,20402
onnxruntime/transformers/onnx_model_bert_keras.py,sha256=iAlnQJJCZMGBUx5fLErc7FInVzBcz14ncc-RAO3svqU,19010
onnxruntime/transformers/onnx_model_bert_tf.py,sha256=Ebq6LeUyxIzE8_LG_xvaPRxtRIlD2gTtVKE5UyeGoRo,25503
onnxruntime/transformers/onnx_model_clip.py,sha256=RyBZh4nOvV7TP_b_SN_nSNgK5noVhtwOHbKhiTCifds,1394
onnxruntime/transformers/onnx_model_conformer.py,sha256=lP4r-iid7Jl1AAOGdZM5S_MCQ__oRq-KTibGcM6Gri4,1412
onnxruntime/transformers/onnx_model_gpt2.py,sha256=3LmzgHuLvO5tyNHKWGidttyqrcpIE7aLBYbRqzjolUg,3913
onnxruntime/transformers/onnx_model_mmdit.py,sha256=NYgWrHAPQRzJ4i9DzbbTeXp_krkI18ZWXRv9djVEBek,4175
onnxruntime/transformers/onnx_model_phi.py,sha256=lChW-NHkl5YWRhwyMCpwMYDSfXFdYmIx456SB3TQbKQ,36339
onnxruntime/transformers/onnx_model_sam2.py,sha256=QW0ZKWjYKknzJgg8Tv7yUlOkIWMaC8qPTXR7pdUaHf0,4947
onnxruntime/transformers/onnx_model_t5.py,sha256=NH_IrumzC_-4prYexpMXy2atjBxXGybzWBbo9q5Qjt4,38039
onnxruntime/transformers/onnx_model_tnlr.py,sha256=6MlEn9yRlF3jVW1zd9NCR0qxnPgIa2dEhaylvbf8dCo,8405
onnxruntime/transformers/onnx_model_unet.py,sha256=yTZFTbRLY-2GpRmPk38QuaR_iJy1RCU61UmQXXMNEHI,9479
onnxruntime/transformers/onnx_model_vae.py,sha256=JTp3ctfgHR7YERn9cNYpXB4ZM82X3CgTYHtVdgrJqxY,1513
onnxruntime/transformers/onnx_utils.py,sha256=GGkO0ebsoPvVOPNfeYC0tN9DJWiBaLXXzjQhBrAXvNE,2175
onnxruntime/transformers/optimizer.py,sha256=HAh9jJGO6QqRHR--4rUOnZbGktMfH98q_7Ab-QG6q18,25685
onnxruntime/transformers/past_helper.py,sha256=uDRilngPAzWQ-ZCNiOivB_qQUroOSSZu6A2EZg6mu0o,6955
onnxruntime/transformers/profile_result_processor.py,sha256=fZSWV2p3QWkG2cgqfEt1dC9rglnY8UgviCWs6W4agek,12940
onnxruntime/transformers/profiler.py,sha256=PEtiO3PQ3h0TWqwdZUu31PDDBV3pMqXM67hYY7PJT7E,13668
onnxruntime/transformers/quantize_helper.py,sha256=HI8aTSVd3bUEcaH2QFyWhqJeevXZiXsE142bb-QJu5U,2893
onnxruntime/transformers/shape_infer_helper.py,sha256=v7VKU2m7blQp21PBbDPeySgwFMoP_BC1OM_rUIlnAFo,4566
onnxruntime/transformers/shape_optimizer.py,sha256=McbgtLsmb3j0AVHWnVigktDgxMktE0UXAdyemg3NNjU,15468
onnxruntime/transformers/torch_onnx_export_helper.py,sha256=DOTqWF9DEbxsxqKWtq3NCqcA7de-JSMgjS-MyczJimg,2575
