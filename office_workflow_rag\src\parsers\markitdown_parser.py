"""
MarkItDown解析器
使用微软开源的MarkItDown库进行文档转换
"""

from pathlib import Path
from typing import Dict, Any, Optional
import logging

try:
    from markitdown import MarkItDown
except ImportError:
    MarkItDown = None

logger = logging.getLogger(__name__)


class MarkItDownParser:
    """MarkItDown解析器"""
    
    def __init__(self):
        if MarkItDown is None:
            raise ImportError("markitdown library not installed. Run: pip install markitdown")
        
        self.converter = MarkItDown()
    
    def parse(self, file_path: Path, **kwargs) -> Dict[str, Any]:
        """
        使用MarkItDown解析文档
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            解析结果字典
        """
        try:
            # 使用MarkItDown转换文档
            result = self.converter.convert(str(file_path))
            
            return {
                'content': result.text_content,
                'markdown': result.text_content,  # MarkItDown直接输出markdown
                'metadata': {
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'file_type': file_path.suffix.lower(),
                    'parser': 'MarkItDown'
                },
                'success': True,
                'error': None
            }
            
        except Exception as e:
            logger.error(f"MarkItDown parsing failed for {file_path}: {e}")
            return {
                'content': '',
                'markdown': '',
                'metadata': {
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'file_type': file_path.suffix.lower(),
                    'parser': 'MarkItDown'
                },
                'success': False,
                'error': str(e)
            }
    
    def is_supported(self, file_path: Path) -> bool:
        """检查文件是否支持"""
        supported_extensions = {'.docx', '.pptx', '.xlsx', '.pdf'}
        return file_path.suffix.lower() in supported_extensions
    
    @staticmethod
    def get_supported_formats():
        """获取支持的文件格式"""
        return ['.docx', '.pptx', '.xlsx', '.pdf']
