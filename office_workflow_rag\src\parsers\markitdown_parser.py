"""
MarkItDown解析器
使用微软开源的MarkItDown库进行文档转换
"""

from pathlib import Path
from typing import Dict, Any, Union
import logging

from .base_parser import BaseParser, ParseResult

try:
    from markitdown import MarkItDown
except ImportError:
    MarkItDown = None

logger = logging.getLogger(__name__)


class MarkItDownParser(BaseParser):
    """MarkItDown解析器"""
    
    def __init__(self):
        if MarkItDown is None:
            raise ImportError("markitdown library not installed. Run: uv pip install markitdown")
        
        self.converter = MarkItDown()
    
    def parse(self, file_path: Union[str, Path], **kwargs) -> ParseResult:
        """
        使用MarkItDown解析文档

        Args:
            file_path: 文件路径（支持str或Path）
            **kwargs: 额外参数

        Returns:
            ParseResult: 解析结果
        """
        # 标准化路径
        path = Path(file_path)

        try:
            # 使用MarkItDown转换文档
            result = self.converter.convert(str(path))

            # 获取基础元数据
            metadata = self._get_file_metadata(path)
            metadata.update({'parser': 'MarkItDown'})

            return ParseResult(
                content=result.text_content,
                markdown=result.text_content,  # MarkItDown直接输出markdown
                metadata=metadata,
                success=True,
                parser_used='MarkItDownParser'
            )

        except Exception as e:
            logger.error(f"MarkItDown parsing failed for {path}: {e}")

            # 获取基础元数据（即使解析失败）
            metadata = self._get_file_metadata(path)
            metadata.update({'parser': 'MarkItDown'})

            return ParseResult(
                content='',
                markdown='',
                metadata=metadata,
                success=False,
                error=str(e),
                parser_used='MarkItDownParser'
            )
    
    def is_supported(self, file_path: Union[str, Path]) -> bool:
        """检查文件是否支持"""
        path = Path(file_path)
        supported_extensions = {'.docx', '.pptx', '.xlsx', '.pdf'}
        return path.suffix.lower() in supported_extensions
    
    @staticmethod
    def get_supported_formats():
        """获取支持的文件格式"""
        return ['.docx', '.pptx', '.xlsx', '.pdf']
