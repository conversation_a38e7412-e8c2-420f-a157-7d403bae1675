"""
解析器基类
定义所有解析器的统一接口
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any
from pydantic import BaseModel


class ParseResult(BaseModel):
    """解析结果数据模型"""
    content: str
    markdown: str
    metadata: Dict[str, Any]
    success: bool
    error: str = None
    parser_used: str = None


class BaseParser(ABC):
    """解析器基类"""
    
    @abstractmethod
    def parse(self, file_path: Path, **kwargs) -> ParseResult:
        """
        解析文档
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            ParseResult: 解析结果
        """
        pass
    
    @abstractmethod
    def is_supported(self, file_path: Path) -> bool:
        """
        检查文件是否支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持
        """
        pass
    
    @staticmethod
    @abstractmethod
    def get_supported_formats():
        """获取支持的文件格式列表"""
        pass
