"""
解析器基类
定义所有解析器的统一接口
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, Union
from pydantic import BaseModel, field_validator


class ParseResult(BaseModel):
    """解析结果数据模型"""
    content: str
    markdown: str
    metadata: Dict[str, Any]
    success: bool
    error: str = None
    parser_used: str = None

    @field_validator('metadata')
    @classmethod
    def validate_metadata(cls, v):
        """确保metadata中的路径是字符串格式"""
        if 'file_path' in v and isinstance(v['file_path'], Path):
            v['file_path'] = str(v['file_path'])
        return v


class BaseParser(ABC):
    """解析器基类"""

    @staticmethod
    def _get_file_metadata(file_path: Path) -> Dict[str, Any]:
        """获取文件基础元数据"""
        return {
            'file_path': str(file_path.resolve()),
            'file_name': file_path.name,
            'file_size': file_path.stat().st_size if file_path.exists() else 0,
            'file_type': file_path.suffix.lower(),
            'parent_dir': str(file_path.parent)
        }

    @abstractmethod
    def parse(self, file_path: Union[str, Path], **kwargs) -> ParseResult:
        """
        解析文档

        Args:
            file_path: 文件路径（支持str或Path）
            **kwargs: 额外参数

        Returns:
            ParseResult: 解析结果
        """
        pass

    @abstractmethod
    def is_supported(self, file_path: Union[str, Path]) -> bool:
        """
        检查文件是否支持

        Args:
            file_path: 文件路径（支持str或Path）

        Returns:
            bool: 是否支持
        """
        pass

    @staticmethod
    @abstractmethod
    def get_supported_formats():
        """获取支持的文件格式列表"""
        pass
