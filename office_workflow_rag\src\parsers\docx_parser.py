"""
Word文档解析器
使用python-docx库进行精细化Word文档处理
"""

from pathlib import Path
from typing import Dict, Any, List
import logging

try:
    from docx import Document
    from docx.document import Document as DocumentType
    from docx.table import Table
    from docx.text.paragraph import Paragraph
except ImportError:
    Document = None

logger = logging.getLogger(__name__)


class DocxParser:
    """Word文档解析器"""
    
    def __init__(self):
        if Document is None:
            raise ImportError("python-docx library not installed. Run: pip install python-docx")
    
    def parse(self, file_path: Path, **kwargs) -> Dict[str, Any]:
        """
        解析Word文档
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
                - include_tables: 是否包含表格 (默认True)
                - include_headers: 是否包含页眉页脚 (默认True)
                - preserve_formatting: 是否保留格式信息 (默认False)
                
        Returns:
            解析结果字典
        """
        include_tables = kwargs.get('include_tables', True)
        include_headers = kwargs.get('include_headers', True)
        preserve_formatting = kwargs.get('preserve_formatting', False)
        
        try:
            doc = Document(str(file_path))
            
            # 提取文档内容
            content_parts = []
            markdown_parts = []
            
            # 处理段落
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    content_parts.append(text)
                    
                    # 转换为Markdown格式
                    if preserve_formatting:
                        markdown_text = self._paragraph_to_markdown(paragraph)
                    else:
                        markdown_text = text
                    
                    markdown_parts.append(markdown_text)
            
            # 处理表格
            if include_tables:
                for table in doc.tables:
                    table_content = self._extract_table_content(table)
                    content_parts.append(table_content['text'])
                    markdown_parts.append(table_content['markdown'])
            
            # 处理页眉页脚
            if include_headers:
                header_footer_content = self._extract_headers_footers(doc)
                if header_footer_content:
                    content_parts.append(header_footer_content)
                    markdown_parts.append(header_footer_content)
            
            return {
                'content': '\n\n'.join(content_parts),
                'markdown': '\n\n'.join(markdown_parts),
                'metadata': {
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'file_type': file_path.suffix.lower(),
                    'parser': 'python-docx',
                    'paragraph_count': len(doc.paragraphs),
                    'table_count': len(doc.tables),
                    'core_properties': self._extract_core_properties(doc)
                },
                'success': True,
                'error': None
            }
            
        except Exception as e:
            logger.error(f"python-docx parsing failed for {file_path}: {e}")
            return {
                'content': '',
                'markdown': '',
                'metadata': {
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'file_type': file_path.suffix.lower(),
                    'parser': 'python-docx'
                },
                'success': False,
                'error': str(e)
            }
    
    def _paragraph_to_markdown(self, paragraph: Paragraph) -> str:
        """将段落转换为Markdown格式"""
        text = paragraph.text
        
        # 简单的格式转换
        if paragraph.style.name.startswith('Heading'):
            level = int(paragraph.style.name.split()[-1]) if paragraph.style.name.split()[-1].isdigit() else 1
            return f"{'#' * level} {text}"
        
        return text
    
    def _extract_table_content(self, table: Table) -> Dict[str, str]:
        """提取表格内容"""
        rows = []
        markdown_rows = []
        
        for i, row in enumerate(table.rows):
            row_cells = [cell.text.strip() for cell in row.cells]
            rows.append(' | '.join(row_cells))
            
            # Markdown表格格式
            if i == 0:  # 表头
                markdown_rows.append('| ' + ' | '.join(row_cells) + ' |')
                markdown_rows.append('| ' + ' | '.join(['---'] * len(row_cells)) + ' |')
            else:
                markdown_rows.append('| ' + ' | '.join(row_cells) + ' |')
        
        return {
            'text': '\n'.join(rows),
            'markdown': '\n'.join(markdown_rows)
        }
    
    def _extract_headers_footers(self, doc: DocumentType) -> str:
        """提取页眉页脚"""
        content = []
        
        for section in doc.sections:
            # 页眉
            if section.header:
                for paragraph in section.header.paragraphs:
                    text = paragraph.text.strip()
                    if text:
                        content.append(f"Header: {text}")
            
            # 页脚
            if section.footer:
                for paragraph in section.footer.paragraphs:
                    text = paragraph.text.strip()
                    if text:
                        content.append(f"Footer: {text}")
        
        return '\n'.join(content)
    
    def _extract_core_properties(self, doc: DocumentType) -> Dict[str, Any]:
        """提取文档核心属性"""
        props = doc.core_properties
        return {
            'title': props.title,
            'author': props.author,
            'subject': props.subject,
            'created': props.created.isoformat() if props.created else None,
            'modified': props.modified.isoformat() if props.modified else None,
        }
    
    def is_supported(self, file_path: Path) -> bool:
        """检查文件是否支持"""
        return file_path.suffix.lower() == '.docx'
    
    @staticmethod
    def get_supported_formats():
        """获取支持的文件格式"""
        return ['.docx']
