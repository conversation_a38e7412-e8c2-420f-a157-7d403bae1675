"""
文档解析器工厂类
使用hybrid策略：优先MarkItDown，失败时降级到专业库
"""

from pathlib import Path
from typing import Optional, Union
import logging

from .base_parser import BaseParser, ParseResult
from .markitdown_parser import MarkItDownParser
from .docx_parser import DocxParser
from .pptx_parser import PptxParser
from .xlsx_parser import XlsxParser

logger = logging.getLogger(__name__)


class DocumentParserFactory:
    """文档解析器工厂 - 使用hybrid策略"""

    def __init__(self):
        # 初始化所有解析器
        self._markitdown_parser = None
        self._specialized_parsers = {}

        # 延迟初始化，避免导入错误
        self._init_parsers()
    
    def _init_parsers(self):
        """初始化解析器"""
        try:
            self._markitdown_parser = MarkItDownParser()
        except ImportError as e:
            logger.warning(f"MarkItDown parser not available: {e}")
        
        # 初始化专业解析器
        try:
            self._specialized_parsers['docx'] = DocxParser()
        except ImportError as e:
            logger.warning(f"Docx parser not available: {e}")
        
        try:
            self._specialized_parsers['pptx'] = PptxParser()
        except ImportError as e:
            logger.warning(f"Pptx parser not available: {e}")
        
        try:
            self._specialized_parsers['xlsx'] = XlsxParser()
        except ImportError as e:
            logger.warning(f"Xlsx parser not available: {e}")
    
    def get_parser(self, file_path: Union[str, Path]) -> Optional[BaseParser]:
        """
        根据文件类型获取合适的解析器

        Args:
            file_path: 文件路径（支持str或Path）

        Returns:
            解析器实例
        """
        path = Path(file_path)
        file_ext = path.suffix.lower()
        
        # 优先返回MarkItDown解析器
        if self._markitdown_parser and self._markitdown_parser.is_supported(path):
            return self._markitdown_parser
        
        # 降级到专业解析器
        parser_map = {
            '.docx': 'docx',
            '.pptx': 'pptx',
            '.xlsx': 'xlsx',
            '.xls': 'xlsx'  # 使用xlsx解析器处理xls
        }
        
        parser_key = parser_map.get(file_ext)
        if parser_key and parser_key in self._specialized_parsers:
            return self._specialized_parsers[parser_key]
        
        return None
    
    def parse_document(self, file_path: Union[str, Path], **kwargs) -> ParseResult:
        """
        解析文档 - hybrid策略实现

        Args:
            file_path: 文件路径（支持str或Path）
            **kwargs: 解析参数

        Returns:
            ParseResult: 解析结果
        """
        # 标准化路径
        path = Path(file_path)

        if not path.exists():
            return ParseResult(
                content='',
                markdown='',
                metadata={'file_path': str(path)},
                success=False,
                error=f"File not found: {path}",
                parser_used='None'
            )
        
        # 第一步：尝试MarkItDown
        if self._markitdown_parser and self._markitdown_parser.is_supported(path):
            try:
                logger.info(f"Trying MarkItDown parser for {path}")
                result = self._markitdown_parser.parse(path, **kwargs)

                if result.success:
                    logger.info(f"MarkItDown parsing successful for {path}")
                    return result
                else:
                    logger.warning(f"MarkItDown parsing failed: {result.error}")

            except Exception as e:
                logger.error(f"MarkItDown parser error for {path}: {e}")

        # 第二步：降级到专业解析器
        file_ext = path.suffix.lower()
        parser_map = {
            '.docx': 'docx',
            '.pptx': 'pptx',
            '.xlsx': 'xlsx',
            '.xls': 'xlsx'
        }
        
        parser_key = parser_map.get(file_ext)
        if parser_key and parser_key in self._specialized_parsers:
            try:
                logger.info(f"Falling back to {parser_key} parser for {path}")
                specialized_parser = self._specialized_parsers[parser_key]
                result = specialized_parser.parse(path, **kwargs)

                if result.success:
                    # 标记为fallback策略
                    result.parser_used = f"{result.parser_used}_fallback"
                    logger.info(f"Specialized parser successful for {path}")
                    return result
                else:
                    logger.error(f"Specialized parser also failed: {result.error}")

            except Exception as e:
                logger.error(f"Specialized parser error for {path}: {e}")

        # 所有解析器都失败
        return ParseResult(
            content='',
            markdown='',
            metadata={
                'file_path': str(path),
                'file_size': path.stat().st_size,
                'file_type': file_ext
            },
            success=False,
            error=f"No suitable parser available for {file_ext} files",
            parser_used='None'
        )
    
    def get_supported_formats(self) -> list:
        """获取所有支持的文件格式"""
        formats = set()
        
        if self._markitdown_parser:
            formats.update(self._markitdown_parser.get_supported_formats())
        
        for parser in self._specialized_parsers.values():
            formats.update(parser.get_supported_formats())
        
        return sorted(list(formats))
    
    def is_supported(self, file_path: Union[str, Path]) -> bool:
        """检查文件是否支持"""
        path = Path(file_path)
        file_ext = path.suffix.lower()
        return file_ext in self.get_supported_formats()


# 便捷函数
def create_parser_factory() -> DocumentParserFactory:
    """创建解析器工厂的便捷函数"""
    return DocumentParserFactory()


def parse_document(file_path: Union[str, Path], **kwargs) -> ParseResult:
    """解析文档的便捷函数"""
    factory = create_parser_factory()
    return factory.parse_document(file_path, **kwargs)
