"""
文档解析器工厂类
支持多种解析策略：MarkItDown、专业库、混合模式
"""

from enum import Enum
from pathlib import Path
from typing import Optional, Dict, Any
import logging

from .markitdown_parser import MarkItDownParser
from .docx_parser import DocxParser
from .pptx_parser import PptxParser
from .xlsx_parser import XlsxParser

logger = logging.getLogger(__name__)


class ParserStrategy(Enum):
    """解析策略枚举"""
    MARKITDOWN_ONLY = "markitdown_only"  # 只使用MarkItDown
    SPECIALIZED_ONLY = "specialized_only"  # 只使用专业库
    HYBRID = "hybrid"  # 混合模式：优先MarkItDown，失败时使用专业库
    AUTO = "auto"  # 自动选择最佳策略


class DocumentParserFactory:
    """文档解析器工厂"""
    
    def __init__(self, strategy: ParserStrategy = ParserStrategy.HYBRID):
        self.strategy = strategy
        self._parsers = {
            'markitdown': MarkItDownParser(),
            'docx': DocxParser(),
            'pptx': PptxParser(), 
            'xlsx': XlsxParser()
        }
    
    def get_parser(self, file_path: Path, **kwargs) -> Optional[Any]:
        """
        根据文件类型和策略获取合适的解析器
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            解析器实例
        """
        file_ext = file_path.suffix.lower()
        
        if self.strategy == ParserStrategy.MARKITDOWN_ONLY:
            return self._get_markitdown_parser(file_ext)
        
        elif self.strategy == ParserStrategy.SPECIALIZED_ONLY:
            return self._get_specialized_parser(file_ext)
        
        elif self.strategy == ParserStrategy.HYBRID:
            return self._get_hybrid_parser(file_ext)
        
        elif self.strategy == ParserStrategy.AUTO:
            return self._get_auto_parser(file_path, **kwargs)
    
    def _get_markitdown_parser(self, file_ext: str):
        """获取MarkItDown解析器"""
        if file_ext in ['.docx', '.pptx', '.xlsx']:
            return self._parsers['markitdown']
        return None
    
    def _get_specialized_parser(self, file_ext: str):
        """获取专业解析器"""
        parser_map = {
            '.docx': 'docx',
            '.pptx': 'pptx', 
            '.xlsx': 'xlsx',
            '.xls': 'xlsx'  # 使用xlsx解析器处理xls
        }
        
        parser_key = parser_map.get(file_ext)
        return self._parsers.get(parser_key) if parser_key else None
    
    def _get_hybrid_parser(self, file_ext: str):
        """获取混合解析器（优先MarkItDown）"""
        # 优先返回MarkItDown解析器
        markitdown_parser = self._get_markitdown_parser(file_ext)
        if markitdown_parser:
            return markitdown_parser
        
        # 降级到专业解析器
        return self._get_specialized_parser(file_ext)
    
    def _get_auto_parser(self, file_path: Path, **kwargs):
        """自动选择最佳解析器"""
        file_size = file_path.stat().st_size
        file_ext = file_path.suffix.lower()
        
        # 根据文件大小和复杂度选择策略
        if file_size > 10 * 1024 * 1024:  # 大于10MB
            logger.info(f"Large file detected ({file_size} bytes), using specialized parser")
            return self._get_specialized_parser(file_ext)
        
        # 默认使用混合模式
        return self._get_hybrid_parser(file_ext)
    
    def parse_document(self, file_path: Path, **kwargs) -> Dict[str, Any]:
        """
        解析文档
        
        Args:
            file_path: 文件路径
            **kwargs: 解析参数
            
        Returns:
            解析结果字典
        """
        parser = self.get_parser(file_path, **kwargs)
        
        if not parser:
            raise ValueError(f"No suitable parser found for {file_path}")
        
        try:
            result = parser.parse(file_path, **kwargs)
            result['parser_used'] = parser.__class__.__name__
            result['strategy'] = self.strategy.value
            return result
            
        except Exception as e:
            logger.error(f"Primary parser failed for {file_path}: {e}")
            
            # 如果是混合模式且MarkItDown失败，尝试专业解析器
            if (self.strategy == ParserStrategy.HYBRID and 
                isinstance(parser, MarkItDownParser)):
                
                logger.info("Falling back to specialized parser")
                specialized_parser = self._get_specialized_parser(file_path.suffix.lower())
                
                if specialized_parser:
                    try:
                        result = specialized_parser.parse(file_path, **kwargs)
                        result['parser_used'] = specialized_parser.__class__.__name__
                        result['strategy'] = 'hybrid_fallback'
                        return result
                    except Exception as fallback_error:
                        logger.error(f"Fallback parser also failed: {fallback_error}")
            
            raise e


# 便捷函数
def create_parser_factory(strategy: str = "hybrid") -> DocumentParserFactory:
    """创建解析器工厂的便捷函数"""
    strategy_enum = ParserStrategy(strategy)
    return DocumentParserFactory(strategy_enum)
