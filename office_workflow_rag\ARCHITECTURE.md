# 项目架构选型文档

## 核心技术栈

### Python环境
- **Python版本**: 3.13
- **包管理器**: uv
- **虚拟环境**: uv venv

### 工作流框架
- **选择**: LlamaIndex Workflow
### 大模型接口
- **API类型**: OpenAI兼容接口
- **模型选择**: 
  - 主模型: GPT-4o-mini
  - 嵌入模型: text-embedding-3-small
- **接口库**: openai

### 向量数据库
- **选择**: Qdrant
- **替代方案**: ChromaDB, FAISS
- **选择原因**:
  - 适合几万文档的大规模数据
  - 高性能向量搜索
  - 支持分布式部署
  - REST API接口便于扩展

### 文档处理策略
- **主要方案**: 混合解析策略
- **解析器组合**:
  - **MarkItDown**: 微软开源统一转换工具（主要）
  - **python-docx**: Word文档专业处理（备选）
  - **python-pptx**: PowerPoint专业处理（备选）
  - **openpyxl**: Excel专业处理（备选）
  - **xlrd**: 处理老版本xls文件

### 解析策略
- **策略类型**: 
  - `hybrid`: 优先MarkItDown，失败时降级到专业库
  - `markitdown_only`: 仅使用MarkItDown
  - `specialized_only`: 仅使用专业库
  - `auto`: 根据文件大小自动选择

### 数据模型
- **数据类**: Pydantic
- **选择原因**:
  - 自动数据验证
  - 强类型检查
  - 内置JSON序列化支持
  - 性能优秀

### 支持的文档格式
- **Word文档**: .docx, .doc
- **PowerPoint**: .pptx, .ppt  
- **Excel表格**: .xlsx, .xls

### 核心组件架构

#### 解析器层
- **MarkItDownParser**: 统一文档转换
- **DocxParser**: Word文档精细处理
- **PptxParser**: PowerPoint内容提取
- **XlsxParser**: Excel数据处理
- **ParserFactory**: 解析器工厂和策略管理

#### 工作流层
- **DocumentAnalysis**: 文档分析工作流
- **MultiDocQA**: 多文档问答工作流
- **CustomNodes**: 自定义流程节点

#### 核心服务层
- **DocumentProcessor**: 文档处理核心
- **VectorManager**: 向量存储管理
- **LLMClient**: 大模型接口封装

#### 工具层
- **Chunking**: 文档分块策略
- **BatchProcessor**: 批处理工具

### 项目结构设计
```
office_workflow_rag/
├── src/
│   ├── core/           # 核心服务
│   ├── workflows/      # 工作流定义
│   ├── parsers/        # 文档解析器
│   └── utils/          # 工具函数
├── data/
│   ├── documents/      # 原始文档存储
│   │   ├── docx/
│   │   ├── doc/
│   │   ├── pptx/
│   │   ├── ppt/
│   │   ├── xlsx/
│   │   └── xls/
│   ├── processed/      # 处理后数据
│   └── vector_db/      # 向量数据库
├── config/             # 配置文件
└── tests/              # 测试文件
```

### 性能优化策略
- **异步处理**: 基于LlamaIndex Workflow的事件驱动
- **批处理**: 大规模文档分批建立索引
- **增量更新**: 支持文档增量索引
- **内存优化**: 针对大文件的内存管理策略